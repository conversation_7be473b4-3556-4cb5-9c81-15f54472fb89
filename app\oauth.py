from authlib.integrations.starlette_client import OAuth
from starlette.config import Config
from starlette.requests import Request
from sqlalchemy.orm import Session
from app.models import User
from app.config import settings

# OAuth configuration
config = Config(environ={
    'GITHUB_CLIENT_ID': settings.GITHUB_CLIENT_ID,
    'GITHUB_CLIENT_SECRET': settings.GITHUB_CLIENT_SECRET,
})

oauth = OAuth(config)

# Register GitHub OAuth provider
oauth.register(
    name='github',
    client_id=settings.GITHUB_CLIENT_ID,
    client_secret=settings.GITHUB_CLIENT_SECRET,
    authorize_url='https://github.com/login/oauth/authorize',
    access_token_url='https://github.com/login/oauth/access_token',
    client_kwargs={
        'scope': 'user:email'
    },
    # Disable server metadata fetching to avoid issues
    server_metadata=None,
    # Add proper user info endpoint
    userinfo_endpoint='https://api.github.com/user',
)

async def get_github_user_info(token: dict) -> dict:
    """Fetch user information from GitHub API."""
    import httpx
    
    headers = {
        'Authorization': f'token {token["access_token"]}',
        'Accept': 'application/vnd.github.v3+json'
    }
    
    async with httpx.AsyncClient() as client:
        try:
            # Get user profile
            user_response = await client.get('https://api.github.com/user', headers=headers)
            user_response.raise_for_status()
            user_data = user_response.json()
            
            # Get user emails
            emails_response = await client.get('https://api.github.com/user/emails', headers=headers)
            emails_response.raise_for_status()
            emails_data = emails_response.json()
            
            # Find primary email
            primary_email = None
            for email in emails_data:
                if email.get('primary'):
                    primary_email = email['email']
                    break
            
            return {
                'github_id': user_data['id'],
                'username': user_data['login'],
                'name': user_data.get('name'),
                'email': primary_email,
                'avatar_url': user_data.get('avatar_url')
            }
        except httpx.HTTPStatusError as e:
            raise Exception(f"GitHub API error: {e.response.status_code}")
        except Exception as e:
            raise Exception(f"Error fetching user info: {str(e)}")

def get_or_create_user(db: Session, user_info: dict, access_token: str = None) -> User:
    """Get existing user or create new one."""
    user = db.query(User).filter(User.github_id == user_info['github_id']).first()
    
    if not user:
        user = User(
            github_id=user_info['github_id'],
            username=user_info['username'],
            name=user_info.get('name'),
            email=user_info.get('email'),
            avatar_url=user_info.get('avatar_url'),
            access_token=access_token
        )
        db.add(user)
        db.commit()
        db.refresh(user)
    else:
        # Update user information
        user.name = user_info.get('name')
        user.email = user_info.get('email')
        user.avatar_url = user_info.get('avatar_url')
        if access_token:
            user.access_token = access_token
        db.commit()
    
    return user

def get_current_user(request: Request, db: Session) -> User | None:
    """Get current logged-in user from session."""
    user_id = request.session.get('user_id')
    if user_id:
        return db.query(User).filter(User.id == user_id).first()
    return None 