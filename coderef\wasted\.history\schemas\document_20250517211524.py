from pydantic import BaseModel
from typing import List, Dict, Optional
from datetime import datetime

class PlaceholderBase(BaseModel):
    field_name: str
    original_value: str

class PlaceholderCreate(PlaceholderBase):
    pass

class Placeholder(PlaceholderBase):
    id: int
    template_id: int
    created_at: datetime

    class Config:
        from_attributes = True

class TemplateBase(BaseModel):
    filename: str
    original_filename: str

class TemplateCreate(TemplateBase):
    placeholders: List[PlaceholderCreate]

class Template(TemplateBase):
    id: int
    created_at: datetime
    placeholders: List[Placeholder]

    class Config:
        from_attributes = True

class GenerateDocumentRequest(BaseModel):
    placeholder_values: Dict[str, str] 