from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Request
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pathlib import Path
import os
import google.generativeai as genai
from dotenv import load_dotenv
from PyPDF2 import PdfReader
import docx

# Load environment variables
load_dotenv()

# Configure Gemini API
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
genai.configure(api_key=GOOGLE_API_KEY)

app = FastAPI(title="File Upload API")

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# Create templates directory if it doesn't exist
TEMPLATES_DIR = Path("templates")
TEMPLATES_DIR.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

async def extract_docx_text(file_path: Path) -> str:
    """Extract text from DOCX file"""
    try:
        doc = docx.Document(file_path)
        return "\n".join([paragraph.text for paragraph in doc.paragraphs])
    except Exception as e:
        return f"Error extracting DOCX text: {str(e)}"

async def extract_pdf_text(file_path: Path) -> str:
    """Extract text from PDF file"""
    try:
        reader = PdfReader(file_path)
        text = []
        for page in reader.pages:
            text.append(page.extract_text())
        return "\n".join(text)
    except Exception as e:
        return f"Error extracting PDF text: {str(e)}"

async def analyze_with_gemini(content: str, file_type: str, filename: str) -> str:
    """Analyze file contents using Gemini API"""
    try:
        # Use Gemini 2.5 Flash Preview model
        model = genai.GenerativeModel('gemini-2.5-flash-preview-04-17')
        
        prompt = f"""Analyze this {file_type} file named {filename} and provide a detailed summary of its contents.
        Focus on the main points and key information.
        
        Content to analyze:
        {content}
        
        Please provide:
        1. A brief overview
        2. Key points or findings
        3. Any important details or patterns noticed
        """
        
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        return f"Error analyzing content with Gemini: {str(e)}"

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Render the upload form"""
    return templates.TemplateResponse("upload.html", {"request": request})

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a file and return its contents"""
    try:
        contents = await file.read()
        
        # Save the file
        file_path = UPLOAD_DIR / file.filename
        with open(file_path, "wb") as f:
            f.write(contents)
        
        # Get file extension
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
        
        if file_ext in ['pdf', 'docx']:
            return {
                "filename": file.filename,
                "content_type": f"application/{file_ext}",
                "message": "File uploaded successfully"
            }
        else:
            return {
                "filename": file.filename,
                "content_type": "unsupported",
                "message": "File type not supported. Please upload PDF or DOCX files only."
            }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while processing the file: {str(e)}"}
        )

@app.post("/analyze")
async def analyze_file(file: UploadFile = File(...)):
    """Upload a file and analyze its contents using Gemini"""
    try:
        contents = await file.read()
        file_path = UPLOAD_DIR / file.filename
        
        # Save the file temporarily
        with open(file_path, "wb") as f:
            f.write(contents)

        # Get file extension
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
        
        # Only process PDF and DOCX files
        if file_ext not in ['pdf', 'docx']:
            return JSONResponse(
                status_code=400,
                content={"message": "Only PDF and DOCX files are supported"}
            )
        
        # Extract text based on file type
        if file_ext == 'pdf':
            text_content = await extract_pdf_text(file_path)
            file_type = "PDF document"
        else:  # docx
            text_content = await extract_docx_text(file_path)
            file_type = "Word document"
        
        # Check if text extraction was successful
        if text_content.startswith("Error"):
            return JSONResponse(
                status_code=500,
                content={"message": text_content}
            )
        
        # Analyze with Gemini
        analysis = await analyze_with_gemini(text_content, file_type, file.filename)
        
        return {
            "filename": file.filename,
            "file_type": file_type,
            "analysis": analysis
        }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while analyzing the file: {str(e)}"}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 