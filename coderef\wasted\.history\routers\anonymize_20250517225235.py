from fastapi import APIRouter, UploadFile, File, Request, Depends, HTTPException
from fastapi.responses import JSONResponse, FileResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path
import docx
import re
from typing import List, Optional, Dict
import os
from sqlalchemy.orm import Session
from models.database import get_db, Template, Placeholder
from schemas.document import TemplateCreate, Template as TemplateSchema, GenerateDocumentRequest

router = APIRouter(prefix="/anonymize", tags=["anonymize"])

# Create necessary directories
UPLOAD_DIR = Path("uploads")
ANONYMIZED_DIR = Path("anonymized")
GENERATED_DIR = Path("generated")
UPLOAD_DIR.mkdir(exist_ok=True)
ANONYMIZED_DIR.mkdir(exist_ok=True)
GENERATED_DIR.mkdir(exist_ok=True)

# Templates
templates = Jinja2Templates(directory="templates")

# async def anonymize_document(
#     input_path: Path,
#     output_path: Path,
#     fields: List[str] = ["NAME", "PRN", "BRANCH", "BATCH", "SUB"]
# ) -> dict:
#     """
#     Anonymize personal information in a document by replacing with placeholders
#     """
#     try:
#         doc = docx.Document(input_path)
#         replacements_made = {}

#         for para in doc.paragraphs:
#             text = para.text.strip()
#             for field in fields:
#                 # Regex to detect lines like "NAME: John Doe" with flexible whitespace
#                 pattern = re.compile(r'^\s*' + re.escape(field) + r':\s*(.*)$', re.IGNORECASE)
#                 match = pattern.match(text)
#                 if match:
#                     original_value = match.group(1)
#                     if field.upper() == "SUB":
#                         # Leave SUB value unchanged
#                         continue
                    
#                     # Store the replacement for reporting
#                     replacements_made[field] = {
#                         "original": original_value,
#                         "placeholder": f"{{{{{field}}}}}"
#                     }
                    
#                     # New placeholder value
#                     new_text = f"{field}: {{{{{field}}}}}"

#                     # Replace text preserving run formatting
#                     for run in para.runs:
#                         run.text = ""
#                     if para.runs:
#                         para.runs[0].text = new_text
#                     else:
#                         para.text = new_text
#                     break

#         # Save the anonymized document
#         doc.save(output_path)

#         return {
#             "status": "success",
#             "message": "Document anonymized successfully",
#             "replacements": replacements_made
#         }

#     except Exception as e:
#         return {
#             "status": "error",
#             "message": f"Error anonymizing document: {str(e)}",
#             "replacements": {}
#         }





async def anonymize_document(
    input_path: Path,
    output_path: Path,
    fields: List[str] = ["NAME", "PRN", "BRANCH", "BATCH", "SUB"]
) -> dict:
    """
    Anonymize personal information in a document by replacing with placeholders
    """
    try:
        doc = docx.Document(input_path)
        replacements_made = {}

        for para in doc.paragraphs:
            print(f"Processing paragraph: {para.text}")  # Debug print
            text = para.text.strip()
            modified_text = text
            
            # Find all field matches in the paragraph
            field_matches = []
            seen_fields = set()  # Track fields we've already processed (case-insensitive)
            
            for field in fields:
                # Skip if we've already processed this field (case-insensitive)
                if field.upper() in seen_fields:
                    continue
                
                # Look for the field in any case
                pattern = re.compile(r'(?:^|\s)' + field + r':\s*([^:\n]+?)(?=\s+\w+:|$)', re.IGNORECASE)
                for match in pattern.finditer(text):
                    field_matches.append({
                        'field': field.upper(),  # Store field in uppercase for consistency
                        'start': match.start(),
                        'end': match.end(),
                        'original_value': match.group(1).strip(),
                        'full_match': text[match.start():match.end()]
                    })
                    seen_fields.add(field.upper())
            
            # If multiple fields found, handle them specially
            if len(field_matches) > 1:
                # Sort matches by their position in text
                field_matches.sort(key=lambda x: x['start'])
                
                # Create new text with each field on its own line
                modified_text = ""
                for match in field_matches:
                    field = match['field']
                    if field.upper() == "SUB":
                        # Leave SUB value unchanged
                        modified_text += f"{match['full_match']}\n"
                        continue
                    
                    # Store the replacement for reporting
                    replacements_made[field] = {
                        "original": match['original_value'],
                        "placeholder": f"{{{{{field}}}}}"
                    }
                    
                    # Add field with placeholder on new line
                    modified_text += f"{field}: {{{{{field}}}}}\n"
                
                modified_text = modified_text.rstrip()  # Remove trailing newline
            
            # If only one field found, handle it as before
            elif len(field_matches) == 1:
                match = field_matches[0]
                field = match['field']
                if field.upper() != "SUB":
                    replacements_made[field] = {
                        "original": match['original_value'],
                        "placeholder": f"{{{{{field}}}}}"
                    }
                    replacement = f"{field}: {{{{{field}}}}}"
                    modified_text = modified_text.replace(match['full_match'], replacement)
            
            # If the text was modified, update the paragraph
            if modified_text != text:
                # Replace text preserving run formatting
                for run in para.runs:
                    run.text = ""
                if para.runs:
                    para.runs[0].text = modified_text
                else:
                    para.text = modified_text

        # Save the anonymized document
        doc.save(output_path)

        return {
            "status": "success",
            "message": "Document anonymized successfully",
            "replacements": replacements_made
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error anonymizing document: {str(e)}",
            "replacements": {}
        }
    

async def generate_document(
    template_path: Path,
    output_path: Path,
    placeholder_values: Dict[str, str]
) -> bool:
    """
    Generate a document by replacing placeholders with actual values
    """
    try:
        doc = docx.Document(template_path)

        for para in doc.paragraphs:
            for field, value in placeholder_values.items():
                placeholder = f"{{{{{field}}}}}"
                if placeholder in para.text:
                    # Replace placeholder with value
                    for run in para.runs:
                        run.text = run.text.replace(placeholder, value)

        doc.save(output_path)
        return True
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating document: {str(e)}")

@router.get("/")
async def anonymize_page(request: Request):
    """Render the anonymization upload form"""
    return templates.TemplateResponse(
        "anonymize.html",
        {"request": request}
    )

@router.post("/upload")
async def upload_and_anonymize(
    file: UploadFile = File(...),
    fields: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Upload a document and anonymize personal information
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.docx'):
            return JSONResponse(
                status_code=400,
                content={"message": "Only DOCX files are supported"}
            )

        # Parse fields if provided
        field_list = ["NAME", "PRN", "BRANCH", "BATCH", "SUB"]  # Remove duplicate lowercase versions
        
        # Save original file
        input_path = UPLOAD_DIR / file.filename
        with open(input_path, "wb") as f:
            contents = await file.read()
            f.write(contents)
        
        # Create anonymized filename
        output_filename = f"anonymized_{file.filename}"
        output_path = ANONYMIZED_DIR / output_filename
        
        # Perform anonymization
        result = await anonymize_document(input_path, output_path, field_list)
        
        if result["status"] == "success":
            # Save template to database
            template_db = Template(
                filename=output_filename,
                original_filename=file.filename
            )
            db.add(template_db)
            db.flush()

            # Save placeholders
            for field, data in result["replacements"].items():
                placeholder_db = Placeholder(
                    template_id=template_db.id,
                    field_name=field,
                    original_value=data["original"]
                )
                db.add(placeholder_db)
            
            db.commit()

            # Return the anonymized file
            return FileResponse(
                path=output_path,
                filename=output_filename,
                media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                headers={
                    "replacements": str(result["replacements"]),
                    "template_id": str(template_db.id)
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content=result
            )
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred: {str(e)}"}
        )

@router.get("/templates")
async def list_templates(db: Session = Depends(get_db)):
    """List all available templates"""
    templates_db = db.query(Template).all()
    return templates_db

@router.get("/templates/{template_id}")
async def get_template(template_id: int, db: Session = Depends(get_db)):
    """Get template details including placeholders"""
    template = db.query(Template).filter(Template.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    return template

@router.post("/templates/{template_id}/generate")
async def generate_from_template(
    template_id: int,
    request: GenerateDocumentRequest,
    db: Session = Depends(get_db)
):
    """Generate a document from a template with provided values"""
    template = db.query(Template).filter(Template.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    template_path = ANONYMIZED_DIR / template.filename
    if not template_path.exists():
        raise HTTPException(status_code=404, detail="Template file not found")

    # Generate output filename
    output_filename = f"generated_{template.original_filename}"
    output_path = GENERATED_DIR / output_filename

    # Generate document
    await generate_document(template_path, output_path, request.placeholder_values)

    return FileResponse(
        path=output_path,
        filename=output_filename,
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    )

@router.get("/download/{filename}")
async def download_anonymized(filename: str):
    """Download an anonymized document"""
    file_path = ANONYMIZED_DIR / filename
    if not file_path.exists():
        return JSONResponse(
            status_code=404,
            content={"message": "File not found"}
        )
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    )

@router.get("/mt")
async def template_management_page(request: Request):
    """Render the template management page"""
    return templates.TemplateResponse(
        "mt.html",
        {"request": request}
    )

@router.get("/list-files")
async def list_anonymized_files():
    """List all files in the anonymized directory"""
    try:
        files = [f.name for f in ANONYMIZED_DIR.glob("*.docx")]
        return files
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get-placeholders/{filename}")
async def get_file_placeholders(filename: str):
    """Get all placeholders from a specific file"""
    try:
        file_path = ANONYMIZED_DIR / filename
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="File not found")

        doc = docx.Document(file_path)
        placeholders = set()
        
        for para in doc.paragraphs:
            # Find all patterns like {{PLACEHOLDER}}
            matches = re.findall(r'\{\{(\w+)\}\}', para.text)
            placeholders.update(matches)
        
        return sorted(list(placeholders))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/generate/{filename}")
async def generate_from_file(
    filename: str,
    request: GenerateDocumentRequest
):
    """Generate a document from a template file with provided values"""
    try:
        template_path = ANONYMIZED_DIR / filename
        if not template_path.exists():
            raise HTTPException(status_code=404, detail="Template file not found")

        # Generate output filename
        output_filename = f"generated_{filename}"
        output_path = GENERATED_DIR / output_filename

        # Generate document
        await generate_document(template_path, output_path, request.placeholder_values)

        return FileResponse(
            path=output_path,
            filename=output_filename,
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 