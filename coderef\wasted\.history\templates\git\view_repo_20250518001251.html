{% extends "git/base.html" %}

{% block title %}{{ repo_name }} - Repository{% endblock %}

{% block content %}
<div class="mb-8">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
            <div class="relative">
                <button class="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    <i class="fas fa-code"></i>
                    <span class="font-semibold">Code</span>
                    <i class="fas fa-caret-down"></i>
                </button>
            </div>
            <button onclick="forkRepository()" class="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-md border">
                <i class="fas fa-code-branch"></i>
                <span>Fork</span>
                <span class="bg-gray-200 px-2 py-0.5 rounded-full text-sm">0</span>
            </button>
        </div>
    </div>

    <div class="bg-white rounded-lg border">
        <!-- Repository Header -->
        <div class="border-b px-4 py-3">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="flex items-center text-sm text-gray-600">
                        <a href="/git/{{ owner }}" class="hover:text-blue-600 hover:underline">{{ owner }}</a>
                        <span class="mx-1">/</span>
                        <a href="/git/{{ owner }}/{{ repo_name }}" class="font-semibold hover:text-blue-600 hover:underline">{{ repo_name }}</a>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="showUploadModal()" class="flex items-center space-x-1 text-sm px-3 py-1 rounded-md border hover:bg-gray-50">
                        <i class="fas fa-arrow-up"></i>
                        <span>Upload files</span>
                    </button>
                    <button onclick="showNewDirModal()" class="flex items-center space-x-1 text-sm px-3 py-1 rounded-md border hover:bg-gray-50">
                        <i class="fas fa-folder-plus"></i>
                        <span>New directory</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Branch and Path Navigation -->
        <div class="bg-gray-50 px-4 py-2 border-b">
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <button class="flex items-center space-x-2 bg-white px-3 py-1 rounded-md border hover:bg-gray-50">
                        <i class="fas fa-code-branch text-gray-600"></i>
                        <span class="font-semibold">main</span>
                        <i class="fas fa-caret-down"></i>
                    </button>
                </div>
                <div class="flex-1">
                    <div class="flex items-center text-sm bg-white rounded-md border px-3 py-1">
                        <a href="/git/{{ owner }}/{{ repo_name }}" class="text-blue-600 hover:underline">
                            <i class="fas fa-home"></i>
                        </a>
                        {% for part in current_path.split('/') if part %}
                        <span class="mx-2">/</span>
                        <a href="/git/{{ owner }}/{{ repo_name }}?path={{ part }}" class="text-blue-600 hover:underline">
                            {{ part }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- File Browser -->
        <div class="divide-y">
            {% for item in structure %}
                {% if item.path == current_path %}
                    {% for dir in item.dirs %}
                    <div class="flex items-center px-4 py-2 hover:bg-gray-50">
                        <div class="flex-1">
                            <a href="?path={{ item.path + '/' + dir if item.path else dir }}" class="flex items-center group">
                                <i class="fas fa-folder text-blue-400 mr-3 w-5"></i>
                                <span class="group-hover:text-blue-600">{{ dir }}</span>
                            </a>
                        </div>
                        <div class="flex items-center space-x-2 text-gray-500 text-sm">
                            <button onclick="deleteItem('{{ dir }}', true)" class="hover:text-red-600">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                    {% for file in item.files %}
                    <div class="flex items-center px-4 py-2 hover:bg-gray-50">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <i class="fas fa-file-alt text-gray-400 mr-3 w-5"></i>
                                <span>{{ file }}</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4 text-gray-500 text-sm">
                            <a href="/git/{{ owner }}/{{ repo_name }}/raw/{{ current_path + '/' if current_path else '' }}{{ file }}" 
                               class="hover:text-blue-600" target="_blank">
                                <i class="fas fa-download"></i>
                            </a>
                            <button onclick="deleteItem('{{ file }}', false)" class="hover:text-red-600">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}
            {% endfor %}
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="px-6 py-4 border-b">
                <h2 class="text-xl font-semibold">Upload files</h2>
            </div>
            <div class="p-6">
                <form id="uploadForm" class="space-y-4">
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center" 
                         id="dropZone"
                         ondrop="handleDrop(event)"
                         ondragover="handleDragOver(event)"
                         ondragleave="handleDragLeave(event)">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-2">Drag and drop files here or</p>
                        <input type="file" id="files" name="file" multiple class="hidden" onchange="handleFileSelect(event)">
                        <button type="button" onclick="document.getElementById('files').click()" 
                                class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                            Choose files
                        </button>
                        <div id="fileList" class="mt-4 text-left"></div>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-2">
                <button onclick="closeUploadModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                    Cancel
                </button>
                <button onclick="submitUpload()" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600">
                    Upload files
                </button>
            </div>
        </div>
    </div>
</div>

<!-- New Directory Modal -->
<div id="newDirModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b">
                <h2 class="text-xl font-semibold">Create new directory</h2>
            </div>
            <form id="newDirForm" class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Directory name</label>
                    <input type="text" id="dirName" name="path" 
                           class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter directory name"
                           pattern="[a-zA-Z0-9_-]+"
                           title="Only letters, numbers, underscores, and hyphens are allowed"
                           required>
                </div>
            </form>
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-2">
                <button onclick="closeNewDirModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                    Cancel
                </button>
                <button onclick="submitNewDir()" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                    Create directory
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Fork Modal -->
<div id="forkModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b">
                <h2 class="text-xl font-semibold">Fork this repository</h2>
            </div>
            <form id="forkForm" class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">New owner name</label>
                    <input type="text" id="newOwner" name="new_owner" 
                           class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter new owner name"
                           required>
                </div>
            </form>
            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-end space-x-2">
                <button onclick="closeForkModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                    Cancel
                </button>
                <button onclick="submitFork()" class="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600">
                    Fork repository
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const currentPath = new URLSearchParams(window.location.search).get('path') || '';

// File upload handling
let selectedFiles = [];

function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.add('border-blue-500', 'bg-blue-50');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('border-blue-500', 'bg-blue-50');
}

function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const dropZone = event.currentTarget;
    dropZone.classList.remove('border-blue-500', 'bg-blue-50');
    
    const files = event.dataTransfer.files;
    handleFiles(files);
}

function handleFileSelect(event) {
    const files = event.target.files;
    handleFiles(files);
}

function handleFiles(files) {
    selectedFiles = Array.from(files);
    updateFileList();
}

function updateFileList() {
    const fileList = document.getElementById('fileList');
    fileList.innerHTML = '';
    
    selectedFiles.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'flex items-center justify-between py-2';
        fileItem.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-file-alt text-gray-400 mr-2"></i>
                <span>${file.name}</span>
            </div>
            <button onclick="removeFile(${index})" class="text-red-500 hover:text-red-700">
                <i class="fas fa-times"></i>
            </button>
        `;
        fileList.appendChild(fileItem);
    });
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    updateFileList();
}

async function submitUpload() {
    if (selectedFiles.length === 0) {
        alert('Please select files to upload');
        return;
    }
    
    const formData = new FormData();
    selectedFiles.forEach(file => {
        formData.append('file', file);
    });
    formData.append('path', currentPath);
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/upload`, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error uploading files: ' + data.message);
        }
    } catch (error) {
        alert('Error uploading files: ' + error.message);
    }
}

function showUploadModal() {
    document.getElementById('uploadModal').classList.remove('hidden');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('uploadForm').reset();
    selectedFiles = [];
    updateFileList();
}

// Directory creation
function showNewDirModal() {
    document.getElementById('newDirModal').classList.remove('hidden');
}

function closeNewDirModal() {
    document.getElementById('newDirModal').classList.add('hidden');
    document.getElementById('newDirForm').reset();
}

async function submitNewDir() {
    const dirName = document.getElementById('dirName').value.trim();
    if (!dirName) {
        alert('Please enter a directory name');
        return;
    }
    
    const path = currentPath ? `${currentPath}/${dirName}` : dirName;
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/mkdir`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `path=${encodeURIComponent(path)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error creating directory: ' + data.message);
        }
    } catch (error) {
        alert('Error creating directory: ' + error.message);
    }
}

// Fork repository
function forkRepository() {
    document.getElementById('forkModal').classList.remove('hidden');
}

function closeForkModal() {
    document.getElementById('forkModal').classList.add('hidden');
    document.getElementById('forkForm').reset();
}

async function submitFork() {
    const newOwner = document.getElementById('newOwner').value.trim();
    if (!newOwner) {
        alert('Please enter a new owner name');
        return;
    }
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/fork`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `new_owner=${encodeURIComponent(newOwner)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.href = `/git/${newOwner}/${data.new_repo.name}`;
        } else {
            alert('Error forking repository: ' + data.message);
        }
    } catch (error) {
        alert('Error forking repository: ' + error.message);
    }
}

// Delete items
async function deleteItem(name, isDirectory) {
    if (!confirm(`Are you sure you want to delete ${isDirectory ? 'directory' : 'file'} "${name}"?`)) {
        return;
    }
    
    const path = currentPath ? `${currentPath}/${name}` : name;
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `path=${encodeURIComponent(path)}&is_directory=${isDirectory}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error deleting item: ' + data.message);
        }
    } catch (error) {
        alert('Error deleting item: ' + error.message);
    }
}
</script>
{% endblock %} 