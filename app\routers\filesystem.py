from fastapi import APIRouter, Request, Depends, HTTPException, Form, UploadFile, File
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from app.database import get_db
from app.dependencies import require_auth, get_user_repository
from app.services.filesystem_service import FileSystemService
from app.models import User, Repository
from app.utils.helpers import format_file_size, get_file_icon
from datetime import datetime
import logging

logger = logging.getLogger(__name__)
router = APIRouter()
templates = Jinja2Templates(directory="templates")
fs_service = FileSystemService()

@router.get("/", response_class=HTMLResponse)
async def filesystem_explorer(
    request: Request,
    path: str = "/",
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
):
    """File system explorer page."""
    try:
        contents = fs_service.get_directory_contents(db, repository, path)
        breadcrumbs = get_path_segments(path)
        
        return templates.TemplateResponse("filesystem/explorer.html", {
            "request": request,
            "user": user,
            "repository": repository,
            "current_path": path,
            "contents": contents,
            "breadcrumbs": breadcrumbs,
            "now": datetime.now(),
            "format_file_size": format_file_size,
            "get_file_icon": get_file_icon
        })
        
    except Exception as e:
        logger.error(f"Error loading filesystem: {e}")
        return templates.TemplateResponse("filesystem/explorer.html", {
            "request": request,
            "user": user,
            "repository": repository,
            "current_path": path,
            "contents": [],
            "breadcrumbs": [],
            "error": "Failed to load directory contents",
            "now": datetime.now(),
            "format_file_size": format_file_size,
            "get_file_icon": get_file_icon
        })

@router.post("/create-folder")
async def create_folder(
    path: str = Form(...),
    name: str = Form(...),
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
):
    """Create a new folder."""
    try:
        folder = fs_service.create_folder(db, repository, path, name)
        
        return JSONResponse(content={
            "success": True,
            "message": f"Folder '{name}' created successfully",
            "folder": {
                "id": folder.id,
                "name": folder.name,
                "path": folder.get_full_path()
            }
        })
        
    except ValueError as e:
        return JSONResponse(content={"error": str(e)}, status_code=400)
    except Exception as e:
        logger.error(f"Error creating folder: {e}")
        return JSONResponse(content={"error": "Failed to create folder"}, status_code=500)

@router.post("/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    path: str = Form(...),
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
):
    """Upload a file."""
    try:
        # Read file content
        file_content = await file.read()
        
        # Upload file
        uploaded_file = fs_service.upload_file(
            db, repository, path, file.filename, file_content
        )
        
        return JSONResponse(content={
            "success": True,
            "message": f"File '{file.filename}' uploaded successfully",
            "file": {
                "id": uploaded_file.id,
                "name": uploaded_file.name,
                "path": uploaded_file.get_full_path(),
                "size": uploaded_file.size
            }
        })
        
    except ValueError as e:
        return JSONResponse(content={"error": str(e)}, status_code=400)
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        return JSONResponse(content={"error": "Failed to upload file"}, status_code=500)

@router.delete("/folder/{folder_id}")
async def delete_folder(
    folder_id: int,
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
):
    """Delete a folder."""
    try:
        success = fs_service.delete_folder(db, folder_id)
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": "Folder deleted successfully"
            })
        else:
            return JSONResponse(content={"error": "Folder not found"}, status_code=404)
            
    except ValueError as e:
        return JSONResponse(content={"error": str(e)}, status_code=400)
    except Exception as e:
        logger.error(f"Error deleting folder: {e}")
        return JSONResponse(content={"error": "Failed to delete folder"}, status_code=500)

@router.delete("/file/{file_id}")
async def delete_file(
    file_id: int,
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
):
    """Delete a file."""
    try:
        success = fs_service.delete_file(db, file_id)
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": "File deleted successfully"
            })
        else:
            return JSONResponse(content={"error": "File not found"}, status_code=404)
            
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        return JSONResponse(content={"error": "Failed to delete file"}, status_code=500)

def get_path_segments(path: str):
    """Get path segments for breadcrumb navigation."""
    if path == "/" or not path:
        return [{"name": "Root", "path": "/"}]
    
    segments = [{"name": "Root", "path": "/"}]
    parts = [p for p in path.split("/") if p]
    
    current_path = ""
    for part in parts:
        current_path += "/" + part
        segments.append({"name": part, "path": current_path})
    
    return segments
