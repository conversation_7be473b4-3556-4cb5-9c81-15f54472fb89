{% extends "base.html" %}

{% block title %}Login - WastedTime{% endblock %}

{% block content %}
<div class="text-center" style="margin-top: 4rem;">
    <div class="card" style="max-width: 400px; margin: 0 auto;">
        <div class="card-body">
            <h1 class="mb-4">
                <i class="fas fa-code-branch"></i> WastedTime
            </h1>
            <p class="text-muted mb-4">
                A virtual file system with GitHub integration
            </p>
            
            {% if request.query_params.get('error') == 'auth_failed' %}
            <div class="alert alert-danger">
                Authentication failed. Please try again.
            </div>
            {% endif %}
            
            <a href="/auth/github" class="btn btn-primary" style="width: 100%;">
                <i class="fab fa-github"></i> Login with GitHub
            </a>
            
            <div class="mt-4">
                <small class="text-muted">
                    Sign in with your GitHub account to create and manage your virtual repository.
                </small>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <h3>Features</h3>
        <div class="row" style="display: flex; gap: 2rem; justify-content: center; margin-top: 2rem;">
            <div class="card" style="max-width: 300px;">
                <div class="card-body text-center">
                    <i class="fas fa-folder-open fa-2x text-primary mb-3"></i>
                    <h5>Virtual File System</h5>
                    <p class="text-muted">Create and manage folders and files in a virtual environment</p>
                </div>
            </div>
            
            <div class="card" style="max-width: 300px;">
                <div class="card-body text-center">
                    <i class="fab fa-github fa-2x text-primary mb-3"></i>
                    <h5>GitHub Integration</h5>
                    <p class="text-muted">Sync your virtual repository with GitHub seamlessly</p>
                </div>
            </div>
            
            <div class="card" style="max-width: 300px;">
                <div class="card-body text-center">
                    <i class="fas fa-user fa-2x text-primary mb-3"></i>
                    <h5>One Repository</h5>
                    <p class="text-muted">Each user gets exactly one virtual repository to manage</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
