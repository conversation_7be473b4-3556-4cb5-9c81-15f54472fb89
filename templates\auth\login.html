{% extends "base.html" %}

{% block title %}Login - WastedTime{% endblock %}

{% block content %}
<div class="text-center" style="margin-top: 4rem;">
    <div class="card" style="max-width: 400px; margin: 0 auto;">
        <div class="card-body">
            <h1 class="mb-4">
                <i class="fas fa-code-branch"></i> WastedTime
            </h1>
            <p class="text-muted mb-4">
                A virtual file system with GitHub integration
            </p>
            
            {% set error = request.query_params.get('error') %}
            {% if error %}
            <div class="alert alert-danger">
                {% if error == 'auth_failed' %}
                    Authentication failed. Please try again.
                {% elif error == 'oauth_error' %}
                    OAuth error occurred. Please try again.
                {% elif error == 'token_failed' %}
                    Failed to exchange authorization code. Please try again.
                {% elif error == 'no_token' %}
                    No access token received from GitHub. Please try again.
                {% elif error == 'user_info_failed' %}
                    Failed to get user information from GitHub. Please try again.
                {% elif error == 'database_error' %}
                    Database error occurred. Please contact support.
                {% else %}
                    An error occurred during authentication. Please try again.
                {% endif %}
            </div>
            {% endif %}
            
            <a href="/auth/github" class="btn btn-primary" style="width: 100%;">
                <i class="fab fa-github"></i> Login with GitHub
            </a>
            
            <div class="mt-4">
                <small class="text-muted">
                    Sign in with your GitHub account to create and manage your virtual repository.
                </small>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <h3>Features</h3>
        <div class="row" style="display: flex; gap: 2rem; justify-content: center; margin-top: 2rem;">
            <div class="card" style="max-width: 300px;">
                <div class="card-body text-center">
                    <i class="fas fa-folder-open fa-2x text-primary mb-3"></i>
                    <h5>Virtual File System</h5>
                    <p class="text-muted">Create and manage folders and files in a virtual environment</p>
                </div>
            </div>
            
            <div class="card" style="max-width: 300px;">
                <div class="card-body text-center">
                    <i class="fab fa-github fa-2x text-primary mb-3"></i>
                    <h5>GitHub Integration</h5>
                    <p class="text-muted">Sync your virtual repository with GitHub seamlessly</p>
                </div>
            </div>
            
            <div class="card" style="max-width: 300px;">
                <div class="card-body text-center">
                    <i class="fas fa-user fa-2x text-primary mb-3"></i>
                    <h5>One Repository</h5>
                    <p class="text-muted">Each user gets exactly one virtual repository to manage</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
