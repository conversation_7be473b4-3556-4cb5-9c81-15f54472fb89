{% extends "base.html" %}

{% block title %}My Repositories - GitHub OAuth App{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">My Repositories</h1>
        <button onclick="openCreateRepoModal()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-md transition duration-300">
            <i class="fas fa-plus mr-2"></i>Create Repository
        </button>
    </div>

    <!-- Repositories Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for repo in repositories %}
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">
                        <a href="{{ repo.html_url }}" target="_blank" class="hover:text-blue-600">
                            {{ repo.name }}
                        </a>
                    </h3>
                    {% if repo.description %}
                        <p class="text-gray-600 text-sm mb-2">{{ repo.description }}</p>
                    {% endif %}
                    <div class="flex items-center text-xs text-gray-500">
                        {% if repo.language %}
                            <span class="mr-4">
                                <i class="fas fa-code mr-1"></i>{{ repo.language }}
                            </span>
                        {% endif %}
                        {% if repo.private %}
                            <span class="text-red-500">
                                <i class="fas fa-lock mr-1"></i>Private
                            </span>
                        {% else %}
                            <span class="text-green-500">
                                <i class="fas fa-globe mr-1"></i>Public
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="border-t pt-4">
                <div class="flex space-x-2">
                    <button onclick="openUploadModal('{{ repo.name }}')" 
                            class="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-sm py-2 px-3 rounded transition duration-300">
                        <i class="fas fa-upload mr-1"></i>Upload File
                    </button>
                    <a href="/repositories/{{ repo.name }}/files" 
                       class="flex-1 bg-gray-500 hover:bg-gray-600 text-white text-sm py-2 px-3 rounded text-center transition duration-300">
                        <i class="fas fa-folder mr-1"></i>View Files
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    {% if not repositories %}
    <div class="text-center py-12">
        <i class="fas fa-folder-open text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-600 mb-2">No repositories found</h3>
        <p class="text-gray-500 mb-6">Create your first repository to get started</p>
        <button onclick="openCreateRepoModal()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-md transition duration-300">
            <i class="fas fa-plus mr-2"></i>Create Your First Repository
        </button>
    </div>
    {% endif %}
</div>

<!-- Create Repository Modal -->
<div id="createRepoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Create New Repository</h3>
                <button onclick="closeCreateRepoModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form action="/repositories/create" method="POST">
                <div class="mb-4">
                    <label for="repo_name" class="block text-sm font-medium text-gray-700 mb-2">Repository Name</label>
                    <input type="text" id="repo_name" name="repo_name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="my-awesome-project">
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="A brief description of your project"></textarea>
                </div>
                
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="private" class="mr-2">
                        <span class="text-sm text-gray-700">Make this repository private</span>
                    </label>
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="closeCreateRepoModal()" 
                            class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md transition duration-300">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md transition duration-300">
                        Create Repository
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Upload File Modal -->
<div id="uploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Upload File</h3>
                <button onclick="closeUploadModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form action="/repositories/upload" method="POST" enctype="multipart/form-data">
                <input type="hidden" id="upload_repo_name" name="repo_name">
                
                <div class="mb-4">
                    <label for="file" class="block text-sm font-medium text-gray-700 mb-2">Choose File</label>
                    <input type="file" id="file" name="file" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="mb-4">
                    <label for="file_path" class="block text-sm font-medium text-gray-700 mb-2">File Path (Optional)</label>
                    <input type="text" id="file_path" name="file_path"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Leave empty to use original filename">
                    <p class="text-xs text-gray-500 mt-1">e.g., docs/readme.txt or images/logo.png</p>
                </div>
                
                <div class="mb-6">
                    <label for="commit_message" class="block text-sm font-medium text-gray-700 mb-2">Commit Message</label>
                    <input type="text" id="commit_message" name="commit_message"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Upload new file">
                </div>
                
                <div class="flex space-x-3">
                    <button type="button" onclick="closeUploadModal()" 
                            class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md transition duration-300">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition duration-300">
                        Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openCreateRepoModal() {
    document.getElementById('createRepoModal').classList.remove('hidden');
}

function closeCreateRepoModal() {
    document.getElementById('createRepoModal').classList.add('hidden');
}

function openUploadModal(repoName) {
    document.getElementById('upload_repo_name').value = repoName;
    document.getElementById('uploadModal').classList.remove('hidden');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
}

// Close modals when clicking outside
document.getElementById('createRepoModal').addEventListener('click', function(e) {
    if (e.target === this) closeCreateRepoModal();
});

document.getElementById('uploadModal').addEventListener('click', function(e) {
    if (e.target === this) closeUploadModal();
});
</script>
{% endblock %} 