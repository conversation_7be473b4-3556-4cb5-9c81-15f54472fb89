# File Upload API

A simple FastAPI application that provides endpoints for file upload and retrieval.

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the server:
```bash
python main.py
```

The server will start at `http://localhost:8000`

## API Endpoints

### 1. Root Endpoint
- **URL**: `/`
- **Method**: `GET`
- **Description**: Returns a welcome message
- **Response**: `{"message": "Welcome to the File Upload API"}`

### 2. File Upload Endpoint
- **URL**: `/upload`
- **Method**: `POST`
- **Description**: Upload a file and get its contents
- **Request**: Form data with file
- **Response**: 
  - For text files: Returns filename, content type, and file contents
  - For binary files: Returns filename, content type, and file size

## Interactive API Documentation

Visit `http://localhost:8000/docs` for the interactive Swagger documentation.

## Notes

- Uploaded files are stored in the `uploads` directory
- The API supports both text and binary files
- For text files, the contents will be returned in the response
- For binary files, file information will be returned instead of raw contents 