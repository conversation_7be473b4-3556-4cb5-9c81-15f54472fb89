<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}GitHub OAuth App{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0 flex items-center">
                        <i class="fab fa-github text-2xl text-gray-800 mr-2"></i>
                        <span class="font-bold text-xl text-gray-800">GitHub OAuth</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    {% if user %}
                        <div class="flex items-center space-x-3">
                            {% if user.avatar_url %}
                                <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="w-8 h-8 rounded-full">
                            {% endif %}
                            <span class="text-gray-700">{{ user.username }}</span>
                            <a href="/repositories" class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-md text-sm font-medium transition duration-300">
                                <i class="fas fa-folder mr-1"></i>Repos
                            </a>
                            <a href="/logout" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                                Logout
                            </a>
                        </div>
                    {% else %}
                        <a href="/login" class="bg-gray-800 hover:bg-gray-900 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                            <i class="fab fa-github mr-2"></i>Login with GitHub
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {% block content %}{% endblock %}
    </main>

    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-gray-500 text-sm">
                Built with <i class="fas fa-heart text-red-500"></i> using FastAPI, SQLAlchemy, and GitHub OAuth
            </p>
        </div>
    </footer>
</body>
</html> 