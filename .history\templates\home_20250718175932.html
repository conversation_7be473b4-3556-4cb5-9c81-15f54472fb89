<!DOCTYPE html>
<html>
<head>
    <title>Home - Wasted Time</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        .user-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: block;
        }
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
        .logout-link {
            background: #dc3545 !important;
        }
        .logout-link:hover {
            background: #c82333 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Wasted Time</h1>
            <p>Your Home Page</p>
        </div>
        
        <div class="user-card">
            {% if user.avatar_url %}
            <img src="{{ user.avatar_url }}" alt="Avatar" class="user-avatar">
            {% endif %}
            <h3>{{ user.name or user.username }}</h3>
            <p><strong>Username:</strong> {{ user.username }}</p>
            {% if user.email %}
            <p><strong>Email:</strong> {{ user.email }}</p>
            {% endif %}
            <p><strong>Member since:</strong> {{ user.created_at.strftime('%B %Y') if user.created_at else 'Unknown' }}</p>
        </div>
        
        <div class="nav-links">
            <a href="/dashboard">Dashboard</a>
            <a href="/">Main Page</a>
            <a href="/auth/logout" class="logout-link">Logout</a>
        </div>
    </div>
</body>
</html> 