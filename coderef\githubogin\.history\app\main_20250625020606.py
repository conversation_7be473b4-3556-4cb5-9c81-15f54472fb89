from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, Form, UploadFile, File
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from starlette.middleware.sessions import SessionMiddleware
from sqlalchemy.orm import Session
import logging
import httpx

from .config import settings
from .database import create_tables, get_db, User
from .auth import oauth, get_github_user_info, get_or_create_user, get_current_user
from .github_api import GitHubAPI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="GitHub OAuth App",
    description="A simple FastAPI application with GitHub OAuth authentication",
    version="1.0.0"
)

# Add session middleware
app.add_middleware(SessionMiddleware, secret_key=settings.SECRET_KEY)

# Setup templates
templates = Jinja2Templates(directory="templates")

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    create_tables()
    logger.info("Database tables created successfully")
    
    if not settings.is_configured:
        logger.warning("GitHub OAuth is not configured. Please set GITHUB_CLIENT_ID and GITHUB_CLIENT_SECRET environment variables.")

@app.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    """Home page - shows login or user dashboard."""
    user = get_current_user(request, db)
    return templates.TemplateResponse("index.html", {"request": request, "user": user})

@app.get("/profile", response_class=HTMLResponse)
async def profile(request: Request, db: Session = Depends(get_db)):
    """User profile page - requires authentication."""
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    
    return templates.TemplateResponse("profile.html", {"request": request, "user": user})

@app.get("/login")
async def login(request: Request):
    """Initiate GitHub OAuth login."""
    if not settings.is_configured:
        return templates.TemplateResponse("error.html", {
            "request": request, 
            "error_message": "GitHub OAuth is not configured. Please contact the administrator."
        })
    
    # Clear existing session to avoid state conflicts
    request.session.clear()
    
    redirect_uri = "http://localhost:8000/github-code"
    return await oauth.github.authorize_redirect(request, redirect_uri)

@app.get("/github-code")
async def auth_callback(request: Request, db: Session = Depends(get_db)):
    """Handle GitHub OAuth callback."""
    try:
        # Clear any existing user session before OAuth
        if 'user_id' in request.session:
            del request.session['user_id']
        
        # Get the authorization token with proper state handling
        token = await oauth.github.authorize_access_token(request)
        
        # Get user information from GitHub
        user_info = await get_github_user_info(token)
        
        # Create or update user in database
        user = get_or_create_user(db, user_info, token.get('access_token'))
        
        # Store user ID in session
        request.session['user_id'] = user.id
        
        logger.info(f"User {user.username} logged in successfully")
        return RedirectResponse(url="/", status_code=302)
        
    except Exception as e:
        logger.error(f"OAuth callback error: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": "Authentication failed. Please try again."
        })

@app.get("/logout")
async def logout(request: Request):
    """Logout user by clearing session."""
    request.session.clear()
    return RedirectResponse(url="/", status_code=302)

@app.get("/api/user")
async def get_user_api(request: Request, db: Session = Depends(get_db)):
    """API endpoint to get current user information."""
    user = get_current_user(request, db)
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    return {
        "id": user.id,
        "github_id": user.github_id,
        "username": user.username,
        "name": user.name,
        "email": user.email,
        "avatar_url": user.avatar_url,
        "created_at": user.created_at.isoformat(),
        "updated_at": user.updated_at.isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "oauth_configured": settings.is_configured
    }

@app.get("/favicon.ico")
async def favicon():
    """Favicon endpoint to prevent 404 errors."""
    return {"message": "No favicon configured"}

@app.get("/repositories", response_class=HTMLResponse)
async def repositories(request: Request, db: Session = Depends(get_db)):
    """Display user's GitHub repositories."""
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    
    if not user.access_token:
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": "Please re-login to access GitHub features."
        })
    
    try:
        github_api = GitHubAPI(user.access_token)
        repositories = await github_api.get_user_repositories()
        
        return templates.TemplateResponse("repositories.html", {
            "request": request,
            "user": user,
            "repositories": repositories
        })
    except Exception as e:
        logger.error(f"Error fetching repositories: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": "Failed to fetch repositories. Please try again."
        })

@app.post("/repositories/create")
async def create_repository(
    request: Request,
    repo_name: str = Form(...),
    description: str = Form(""),
    private: bool = Form(False),
    db: Session = Depends(get_db)
):
    """Create a new GitHub repository."""
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    
    if not user.access_token:
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": "Please re-login to access GitHub features."
        })
    
    try:
        github_api = GitHubAPI(user.access_token)
        repo = await github_api.create_repository(repo_name, description, private)
        
        logger.info(f"Repository {repo_name} created successfully by {user.username}")
        return RedirectResponse(url="/repositories", status_code=302)
        
    except Exception as e:
        logger.error(f"Error creating repository: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": f"Failed to create repository: {str(e)}"
        })

@app.post("/repositories/upload")
async def upload_file(
    request: Request,
    repo_name: str = Form(...),
    file: UploadFile = File(...),
    file_path: str = Form(""),
    commit_message: str = Form("Upload new file"),
    db: Session = Depends(get_db)
):
    """Upload a file to a GitHub repository."""
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    
    if not user.access_token:
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": "Please re-login to access GitHub features."
        })
    
    try:
        # Read file content
        content = await file.read()
        
        # Use original filename if no path specified
        target_path = file_path if file_path else file.filename
        
        # Create GitHub API instance with username
        github_api = GitHubAPI(user.access_token)
        
        # Replace placeholder username in the upload_file method
        result = await upload_file_to_repo(github_api, user.username, repo_name, target_path, content, commit_message)
        
        logger.info(f"File {target_path} uploaded to {repo_name} by {user.username}")
        return RedirectResponse(url="/repositories", status_code=302)
        
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": f"Failed to upload file: {str(e)}"
        })

async def upload_file_to_repo(github_api: GitHubAPI, username: str, repo_name: str, file_path: str, content: bytes, commit_message: str):
    """Helper function to upload file with proper username."""
    import base64
    
    encoded_content = base64.b64encode(content).decode('utf-8')
    
    data = {
        "message": commit_message,
        "content": encoded_content
    }
    
    async with httpx.AsyncClient() as client:
        # Check if file exists
        get_response = await client.get(
            f"{github_api.base_url}/repos/{username}/{repo_name}/contents/{file_path}",
            headers=github_api.headers
        )
        
        if get_response.status_code == 200:
            # File exists, add SHA for update
            file_info = get_response.json()
            data["sha"] = file_info["sha"]
        
        # Upload or update the file
        response = await client.put(
            f"{github_api.base_url}/repos/{username}/{repo_name}/contents/{file_path}",
            headers=github_api.headers,
            json=data
        )
        
        if response.status_code in [200, 201]:
            return response.json()
        else:
            response.raise_for_status()

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    return templates.TemplateResponse("error.html", {
        "request": request,
        "error_message": "Page not found."
    }, status_code=404)

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    logger.error(f"Internal server error: {str(exc)}")
    return templates.TemplateResponse("error.html", {
        "request": request,
        "error_message": "Internal server error. Please try again later."
    }, status_code=500)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True) 