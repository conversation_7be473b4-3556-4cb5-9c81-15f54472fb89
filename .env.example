# WastedTime - GitHub Virtual File System Configuration
# Copy this file to .env and update the values
# Never commit the actual .env file to version control!

# GitHub OAuth Configuration
# Create a GitHub OAuth App at: https://github.com/settings/developers
# - Application name: WastedTime
# - Homepage URL: http://localhost:8000
# - Authorization callback URL: http://localhost:8000/auth/callback

GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here

# Application Security
# Generate a strong secret key: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-secret-key-change-in-production

# Database Configuration (SQLite for development)
DATABASE_URL=sqlite:///./wastedtime.db

# File Storage Configuration
STORAGE_DIR=storage
MAX_FILE_SIZE=10485760

# Development Settings
DEBUG=True
ENVIRONMENT=development
