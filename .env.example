# =============================================================================
# WastedTime - GitHub Virtual File System Configuration
# =============================================================================
# Copy this file to .env and update the values according to your environment
# Never commit the actual .env file to version control!

# =============================================================================
# GitHub OAuth Configuration
# =============================================================================
# Create a GitHub OAuth App at: https://github.com/settings/developers
#
# Required settings for your OAuth App:
# - Application name: WastedTime (or your preferred name)
# - Homepage URL: http://localhost:8000 (for development)
# - Authorization callback URL: http://localhost:8000/auth/callback
#
# For production, update URLs to your domain:
# - Homepage URL: https://yourdomain.com
# - Authorization callback URL: https://yourdomain.com/auth/callback

GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here

# =============================================================================
# Application Security Configuration
# =============================================================================
# Generate a strong secret key for session encryption
# You can generate one using: python -c "import secrets; print(secrets.token_urlsafe(32))"
# IMPORTANT: Change this in production and keep it secret!

SECRET_KEY=your-secret-key-change-in-production

# =============================================================================
# Database Configuration
# =============================================================================
# SQLite (Development - Default)
DATABASE_URL=sqlite:///./wastedtime.db

# PostgreSQL (Production - Recommended)
# DATABASE_URL=postgresql://username:password@localhost:5432/wastedtime

# MySQL (Alternative)
# DATABASE_URL=mysql://username:password@localhost:3306/wastedtime

# =============================================================================
# File Storage Configuration
# =============================================================================
# Directory where uploaded files will be stored
# Ensure this directory has proper read/write permissions
STORAGE_DIR=storage

# Maximum file size in bytes (default: 10MB)
# GitHub has a 100MB limit per file, but you may want to set lower limits
# Common values:
# - 1MB: 1048576
# - 5MB: 5242880
# - 10MB: 10485760
# - 25MB: 26214400
# - 50MB: 52428800
# - 100MB: 104857600
MAX_FILE_SIZE=10485760

# Maximum total repository size in bytes (default: 1GB)
# This limits the total size of all files in a user's repository
MAX_REPOSITORY_SIZE=1073741824

# =============================================================================
# Server Configuration
# =============================================================================
# Port for the application to run on
PORT=8000

# Host to bind to (0.0.0.0 for all interfaces, 127.0.0.1 for localhost only)
HOST=0.0.0.0

# Environment mode
# Values: development, production, testing
ENVIRONMENT=development

# Enable debug mode (only for development!)
DEBUG=True

# =============================================================================
# Logging Configuration
# =============================================================================
# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Log file path (leave empty to log to console only)
LOG_FILE=

# Enable request logging
LOG_REQUESTS=True

# =============================================================================
# Session Configuration
# =============================================================================
# Session timeout in seconds (default: 24 hours)
SESSION_TIMEOUT=86400

# Session cookie settings
SESSION_COOKIE_SECURE=False  # Set to True in production with HTTPS
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# =============================================================================
# Rate Limiting Configuration
# =============================================================================
# Enable rate limiting for API endpoints
ENABLE_RATE_LIMITING=True

# Requests per minute per IP
RATE_LIMIT_PER_MINUTE=60

# Requests per hour per user
RATE_LIMIT_PER_HOUR=1000

# =============================================================================
# GitHub API Configuration
# =============================================================================
# GitHub API base URL (usually don't change this)
GITHUB_API_BASE_URL=https://api.github.com

# Request timeout in seconds
GITHUB_API_TIMEOUT=30

# Maximum retries for failed requests
GITHUB_API_MAX_RETRIES=3

# =============================================================================
# File Processing Configuration
# =============================================================================
# Allowed file extensions (comma-separated, leave empty to allow all)
# Example: .txt,.md,.py,.js,.html,.css,.json,.yml,.yaml
ALLOWED_FILE_EXTENSIONS=

# Blocked file extensions (comma-separated)
# Security: Block potentially dangerous file types
BLOCKED_FILE_EXTENSIONS=.exe,.bat,.cmd,.com,.scr,.pif,.vbs,.js,.jar,.app,.deb,.rpm,.dmg,.pkg

# Maximum filename length
MAX_FILENAME_LENGTH=255

# =============================================================================
# Backup Configuration
# =============================================================================
# Enable automatic database backups
ENABLE_AUTO_BACKUP=False

# Backup directory
BACKUP_DIR=backups

# Backup retention days
BACKUP_RETENTION_DAYS=30

# =============================================================================
# Email Configuration (Optional - for notifications)
# =============================================================================
# SMTP settings for sending emails (optional feature)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=True
EMAIL_FROM=<EMAIL>

# =============================================================================
# Monitoring and Analytics (Optional)
# =============================================================================
# Sentry DSN for error tracking (optional)
SENTRY_DSN=

# Google Analytics tracking ID (optional)
GA_TRACKING_ID=

# =============================================================================
# Production-Only Settings
# =============================================================================
# These settings should only be used in production

# Enable HTTPS redirect
FORCE_HTTPS=False

# Trusted hosts (comma-separated)
TRUSTED_HOSTS=localhost,127.0.0.1

# CORS allowed origins (comma-separated)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000

# =============================================================================
# Development-Only Settings
# =============================================================================
# These settings are useful for development and testing

# Enable auto-reload on code changes
AUTO_RELOAD=True

# Enable detailed error pages
SHOW_ERROR_DETAILS=True

# Disable authentication for testing (NEVER use in production!)
DISABLE_AUTH=False

# =============================================================================
# Testing Configuration
# =============================================================================
# Test database URL (used when running tests)
TEST_DATABASE_URL=sqlite:///./test.db

# Test storage directory
TEST_STORAGE_DIR=test_storage

# =============================================================================
# Example Production Configuration
# =============================================================================
# Uncomment and modify these for production deployment:

# ENVIRONMENT=production
# DEBUG=False
# SECRET_KEY=your-super-secret-production-key-here
# DATABASE_URL=**********************************/wastedtime
# FORCE_HTTPS=True
# SESSION_COOKIE_SECURE=True
# TRUSTED_HOSTS=yourdomain.com,www.yourdomain.com
# LOG_LEVEL=WARNING
# LOG_FILE=/var/log/wastedtime/app.log
