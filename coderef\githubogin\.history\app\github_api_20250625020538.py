import httpx
import base64
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class GitHubAPI:
    """GitHub API client for repository and file operations."""
    
    def __init__(self, access_token: str):
        self.access_token = access_token
        self.base_url = "https://api.github.com"
        self.headers = {
            'Authorization': f'token {access_token}',
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        }
    
    async def create_repository(self, name: str, description: str = "", private: bool = False) -> Dict[str, Any]:
        """Create a new GitHub repository."""
        data = {
            "name": name,
            "description": description,
            "private": private,
            "auto_init": True  # Create with README.md
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/user/repos",
                headers=self.headers,
                json=data
            )
            
            if response.status_code == 201:
                return response.json()
            else:
                logger.error(f"Failed to create repository: {response.text}")
                response.raise_for_status()
    
    async def get_user_repositories(self) -> list:
        """Get all repositories for the authenticated user."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/user/repos?sort=created&direction=desc",
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get repositories: {response.text}")
                return []
    
    async def upload_file(self, repo_name: str, file_path: str, content: bytes, 
                         commit_message: str = "Upload file") -> Dict[str, Any]:
        """Upload a file to a GitHub repository."""
        # Encode file content to base64
        encoded_content = base64.b64encode(content).decode('utf-8')
        
        data = {
            "message": commit_message,
            "content": encoded_content
        }
        
        async with httpx.AsyncClient() as client:
            # First, check if file exists to get its SHA (for updates)
            get_response = await client.get(
                f"{self.base_url}/repos/{{username}}/{repo_name}/contents/{file_path}",
                headers=self.headers
            )
            
            if get_response.status_code == 200:
                # File exists, add SHA for update
                file_info = get_response.json()
                data["sha"] = file_info["sha"]
            
            # Upload or update the file
            response = await client.put(
                f"{self.base_url}/repos/{{username}}/{repo_name}/contents/{file_path}",
                headers=self.headers,
                json=data
            )
            
            if response.status_code in [200, 201]:
                return response.json()
            else:
                logger.error(f"Failed to upload file: {response.text}")
                response.raise_for_status()
    
    async def get_repository_files(self, repo_name: str, path: str = "") -> list:
        """Get files in a repository directory."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/repos/{{username}}/{repo_name}/contents/{path}",
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get repository files: {response.text}")
                return [] 