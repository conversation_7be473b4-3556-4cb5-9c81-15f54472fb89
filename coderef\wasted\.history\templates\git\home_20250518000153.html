{% extends "git/base.html" %}

{% block title %}Git Repositories{% endblock %}

{% block content %}
<div class="mb-8">
    <h1 class="text-3xl font-bold mb-4">Repositories</h1>
    <a href="/git/new" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
        <i class="fas fa-plus mr-2"></i>New Repository
    </a>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {% for repo in repos %}
    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
        <div class="flex items-start justify-between">
            <div>
                <h2 class="text-xl font-semibold mb-2">
                    <a href="/git/{{ repo.owner }}/{{ repo.name }}" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-book mr-2"></i>{{ repo.name }}
                    </a>
                </h2>
                <p class="text-gray-600 mb-4">
                    <i class="fas fa-user mr-1"></i>{{ repo.owner }}
                </p>
            </div>
            {% if repo.forked_from %}
            <span class="text-gray-500 text-sm">
                <i class="fas fa-code-branch mr-1"></i>Forked from {{ repo.forked_from.owner }}/{{ repo.forked_from.name }}
            </span>
            {% endif %}
        </div>
        
        <div class="text-sm text-gray-500">
            <p><i class="far fa-clock mr-1"></i>Created: {{ repo.created_at.split('T')[0] }}</p>
        </div>
        
        <div class="mt-4 flex space-x-2">
            <button onclick="location.href='/git/{{ repo.owner }}/{{ repo.name }}'" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                <i class="fas fa-folder-open mr-1"></i>View
            </button>
            <button onclick="forkRepository('{{ repo.owner }}', '{{ repo.name }}')" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                <i class="fas fa-code-branch mr-1"></i>Fork
            </button>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Fork Modal -->
<div id="forkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold mb-4">Fork Repository</h2>
            <form id="forkForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">New Owner Name</label>
                    <input type="text" id="newOwner" name="new_owner" class="w-full border rounded px-3 py-2" required>
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeForkModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Cancel
                    </button>
                    <button type="submit" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Fork
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentRepo = null;

function forkRepository(owner, name) {
    currentRepo = { owner, name };
    document.getElementById('forkModal').classList.remove('hidden');
}

function closeForkModal() {
    document.getElementById('forkModal').classList.add('hidden');
    document.getElementById('forkForm').reset();
}

document.getElementById('forkForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const newOwner = document.getElementById('newOwner').value;
    
    try {
        const response = await fetch(`/git/${currentRepo.owner}/${currentRepo.name}/fork`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `new_owner=${encodeURIComponent(newOwner)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error forking repository: ' + data.message);
        }
    } catch (error) {
        alert('Error forking repository: ' + error.message);
    }
});
</script>
{% endblock %} 