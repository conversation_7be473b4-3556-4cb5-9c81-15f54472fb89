<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Templates</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .template-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .template-title {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 10px;
        }

        .template-info {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
        }

        .placeholder-list {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .placeholder-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .placeholder-label {
            font-weight: 600;
            color: #495057;
        }

        .placeholder-value {
            color: #6c757d;
            font-style: italic;
        }

        .placeholder-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        .btn.download {
            background-color: #28a745;
        }

        .btn.download:hover {
            background-color: #218838;
        }

        #result {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 5px;
            display: none;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateY(100px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        #result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        #result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
            color: #666;
        }

        .loading::after {
            content: "...";
            animation: dots 1.5s steps(5, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60%, 100% { content: "..."; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Document Templates</h1>
        <div class="loading" id="loading">Loading templates</div>
        <div class="templates-grid" id="templatesGrid"></div>
        <div id="result"></div>
    </div>

    <template id="templateCardTemplate">
        <div class="template-card">
            <h2 class="template-title"></h2>
            <div class="template-info"></div>
            <div class="placeholder-list"></div>
            <button class="btn download" onclick="downloadTemplate(this)">Download Template</button>
            <button class="btn" onclick="generateDocument(this)">Generate Document</button>
        </div>
    </template>

    <script>
        const templatesGrid = document.getElementById('templatesGrid');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        const templateCardTemplate = document.getElementById('templateCardTemplate');

        // Load templates when page loads
        window.addEventListener('load', loadTemplates);

        async function loadTemplates() {
            try {
                loading.style.display = 'block';
                const response = await fetch('/anonymize/templates');
                const templates = await response.json();
                
                templatesGrid.innerHTML = '';
                templates.forEach(template => {
                    const card = createTemplateCard(template);
                    templatesGrid.appendChild(card);
                });
            } catch (error) {
                showResult('error', `Error loading templates: ${error.message}`);
            } finally {
                loading.style.display = 'none';
            }
        }

        function createTemplateCard(template) {
            const card = templateCardTemplate.content.cloneNode(true);
            const templateCard = card.querySelector('.template-card');
            
            templateCard.dataset.templateId = template.id;
            templateCard.dataset.filename = template.filename;
            
            templateCard.querySelector('.template-title').textContent = template.original_filename;
            templateCard.querySelector('.template-info').textContent = 
                `Created: ${new Date(template.created_at).toLocaleDateString()}`;

            const placeholderList = templateCard.querySelector('.placeholder-list');
            template.placeholders.forEach(placeholder => {
                const item = document.createElement('div');
                item.className = 'placeholder-item';
                
                const label = document.createElement('div');
                label.className = 'placeholder-label';
                label.textContent = placeholder.field_name;
                
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'placeholder-input';
                input.placeholder = `Enter value for ${placeholder.field_name}`;
                input.dataset.field = placeholder.field_name;
                
                item.appendChild(label);
                item.appendChild(input);
                placeholderList.appendChild(item);
            });

            return card;
        }

        async function downloadTemplate(button) {
            const templateCard = button.closest('.template-card');
            const filename = templateCard.dataset.filename;
            
            try {
                const response = await fetch(`/anonymize/download/${filename}`);
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                } else {
                    throw new Error('Failed to download template');
                }
            } catch (error) {
                showResult('error', `Error downloading template: ${error.message}`);
            }
        }

        async function generateDocument(button) {
            const templateCard = button.closest('.template-card');
            const templateId = templateCard.dataset.templateId;
            
            // Collect values from inputs
            const inputs = templateCard.querySelectorAll('.placeholder-input');
            const placeholderValues = {};
            
            inputs.forEach(input => {
                if (input.value.trim()) {
                    placeholderValues[input.dataset.field] = input.value.trim();
                }
            });

            if (Object.keys(placeholderValues).length === 0) {
                showResult('error', 'Please fill in at least one placeholder value');
                return;
            }

            try {
                const response = await fetch(`/anonymize/templates/${templateId}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        placeholder_values: placeholderValues
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `generated_document.docx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showResult('success', 'Document generated successfully!');
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || 'Failed to generate document');
                }
            } catch (error) {
                showResult('error', `Error generating document: ${error.message}`);
            }
        }

        function showResult(type, message) {
            result.textContent = message;
            result.className = type;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html> 