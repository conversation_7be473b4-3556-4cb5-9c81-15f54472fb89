from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.auth_service import oauth, AuthService
from app.config import settings
import logging

logger = logging.getLogger(__name__)
router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Show login page."""
    return templates.TemplateResponse("auth/login.html", {"request": request})

@router.get("/github")
async def github_login(request: Request):
    """Initiate GitHub OAuth login."""
    if not settings.is_configured:
        raise HTTPException(
            status_code=500,
            detail="GitHub OAuth is not configured. Please contact the administrator."
        )

    # Clear existing session but preserve any necessary data
    user_id = request.session.get('user_id')
    request.session.clear()

    # Set redirect URI based on environment
    if settings.is_production:
        redirect_uri = f"https://{request.url.hostname}/auth/callback"
    else:
        redirect_uri = "http://localhost:8000/auth/callback"

    return await oauth.github.authorize_redirect(request, redirect_uri)

@router.get("/callback")
async def auth_callback(request: Request, db: Session = Depends(get_db)):
    """Handle GitHub OAuth callback."""
    try:
        # Check for error parameter
        error = request.query_params.get('error')
        if error:
            logger.error(f"OAuth error: {error}")
            return RedirectResponse(url="/?error=oauth_error", status_code=302)

        # Get the authorization token
        try:
            token = await oauth.github.authorize_access_token(request)
        except Exception as token_error:
            logger.error(f"Token exchange error: {token_error}")
            return RedirectResponse(url="/?error=token_failed", status_code=302)

        if not token:
            logger.error("No token received from GitHub")
            return RedirectResponse(url="/?error=no_token", status_code=302)

        # Get user information from GitHub
        try:
            user_info = await AuthService.get_github_user_info(token)
        except Exception as user_error:
            logger.error(f"Failed to get user info: {user_error}")
            return RedirectResponse(url="/?error=user_info_failed", status_code=302)

        # Create or update user in database
        try:
            user = AuthService.get_or_create_user(db, user_info, token.get('access_token'))
        except Exception as db_error:
            logger.error(f"Database error: {db_error}")
            return RedirectResponse(url="/?error=database_error", status_code=302)

        # Store user ID in session
        request.session['user_id'] = user.id

        logger.info(f"User {user.username} logged in successfully")
        return RedirectResponse(url="/dashboard", status_code=302)

    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        return RedirectResponse(url="/?error=auth_failed", status_code=302)

@router.get("/logout")
async def logout(request: Request):
    """Logout user."""
    request.session.clear()
    return RedirectResponse(url="/", status_code=302)
