from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from sqlalchemy.orm import Session
from app.database import get_db
from app.services.auth_service import oauth, AuthService
from app.config import settings
import logging

logger = logging.getLogger(__name__)
router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Show login page."""
    return templates.TemplateResponse("auth/login.html", {"request": request})

@router.get("/github")
async def github_login(request: Request):
    """Initiate GitHub OAuth login."""
    if not settings.is_configured:
        raise HTTPException(
            status_code=500, 
            detail="GitHub OAuth is not configured. Please contact the administrator."
        )
    
    # Clear existing session to avoid state conflicts
    request.session.clear()
    
    redirect_uri = "http://localhost:8000/auth/callback"
    return await oauth.github.authorize_redirect(request, redirect_uri)

@router.get("/callback")
async def auth_callback(request: Request, db: Session = Depends(get_db)):
    """Handle GitHub OAuth callback."""
    try:
        # Clear any existing user session before OAuth
        if 'user_id' in request.session:
            del request.session['user_id']
        
        # Get the authorization token
        token = await oauth.github.authorize_access_token(request)
        
        # Get user information from GitHub
        user_info = await AuthService.get_github_user_info(token)
        
        # Create or update user in database
        user = AuthService.get_or_create_user(db, user_info, token.get('access_token'))
        
        # Store user ID in session
        request.session['user_id'] = user.id
        
        logger.info(f"User {user.username} logged in successfully")
        return RedirectResponse(url="/dashboard", status_code=302)
        
    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        return RedirectResponse(url="/?error=auth_failed", status_code=302)

@router.get("/logout")
async def logout(request: Request):
    """Logout user."""
    request.session.clear()
    return RedirectResponse(url="/", status_code=302)
