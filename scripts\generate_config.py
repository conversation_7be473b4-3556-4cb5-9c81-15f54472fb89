#!/usr/bin/env python3
"""
Configuration Generator for WastedTime

This script helps generate secure configuration values for the .env file.
"""

import secrets
import string
import os
import sys
from pathlib import Path

def generate_secret_key(length=32):
    """Generate a secure secret key."""
    return secrets.token_urlsafe(length)

def generate_password(length=16):
    """Generate a secure password."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def create_env_file():
    """Create a .env file with secure defaults."""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if env_file.exists():
        response = input(f".env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Aborted.")
            return
    
    if not env_example.exists():
        print("Error: .env.example file not found!")
        return
    
    # Read the example file
    with open(env_example, 'r') as f:
        content = f.read()
    
    # Generate secure values
    secret_key = generate_secret_key()
    
    # Replace placeholder values
    replacements = {
        'your-secret-key-change-in-production': secret_key,
        'your_github_client_id_here': 'YOUR_GITHUB_CLIENT_ID',
        'your_github_client_secret_here': 'YOUR_GITHUB_CLIENT_SECRET',
    }
    
    for old, new in replacements.items():
        content = content.replace(old, new)
    
    # Write the new .env file
    with open(env_file, 'w') as f:
        f.write(content)
    
    print(f"✅ Created .env file at {env_file}")
    print("\n🔧 Next steps:")
    print("1. Edit the .env file and set your GitHub OAuth credentials:")
    print("   - GITHUB_CLIENT_ID=your_actual_client_id")
    print("   - GITHUB_CLIENT_SECRET=your_actual_client_secret")
    print("2. Review other settings as needed")
    print("3. Run the application with: python run.py")

def show_github_setup_instructions():
    """Show instructions for setting up GitHub OAuth."""
    print("🔧 GitHub OAuth Setup Instructions:")
    print("=" * 50)
    print("1. Go to: https://github.com/settings/developers")
    print("2. Click 'New OAuth App'")
    print("3. Fill in the form:")
    print("   - Application name: WastedTime")
    print("   - Homepage URL: http://localhost:8000")
    print("   - Authorization callback URL: http://localhost:8000/auth/callback")
    print("4. Click 'Register application'")
    print("5. Copy the Client ID and Client Secret to your .env file")
    print("\n📝 For production, update the URLs to your domain:")
    print("   - Homepage URL: https://yourdomain.com")
    print("   - Authorization callback URL: https://yourdomain.com/auth/callback")

def generate_database_config():
    """Generate database configuration examples."""
    print("🗄️  Database Configuration Examples:")
    print("=" * 50)
    
    print("\n📁 SQLite (Development):")
    print("DATABASE_URL=sqlite:///./wastedtime.db")
    
    print("\n🐘 PostgreSQL (Production):")
    db_password = generate_password()
    print(f"DATABASE_URL=postgresql://wastedtime_user:{db_password}@localhost:5432/wastedtime")
    print(f"Generated password: {db_password}")
    
    print("\n🐬 MySQL (Alternative):")
    db_password = generate_password()
    print(f"DATABASE_URL=mysql://wastedtime_user:{db_password}@localhost:3306/wastedtime")
    print(f"Generated password: {db_password}")

def show_security_recommendations():
    """Show security recommendations."""
    print("🔒 Security Recommendations:")
    print("=" * 50)
    print("✅ Use strong, unique secret keys")
    print("✅ Never commit .env file to version control")
    print("✅ Enable HTTPS in production (FORCE_HTTPS=True)")
    print("✅ Use secure session cookies (SESSION_COOKIE_SECURE=True)")
    print("✅ Set appropriate file size limits")
    print("✅ Configure trusted hosts for production")
    print("✅ Enable rate limiting")
    print("✅ Use PostgreSQL for production databases")
    print("✅ Regularly update dependencies")
    print("✅ Monitor application logs")

def validate_configuration():
    """Validate current configuration."""
    project_root = Path(__file__).parent.parent
    env_file = project_root / ".env"

    if not env_file.exists():
        print("❌ No .env file found!")
        print("Run: python scripts/generate_config.py env")
        return

    # Add the project root to Python path to import app modules
    sys.path.insert(0, str(project_root))

    try:
        from app.config import settings
        settings.print_configuration_status()
    except ImportError as e:
        print(f"❌ Error importing configuration: {e}")
        print("Make sure you're in the project root directory")
    except Exception as e:
        print(f"❌ Error validating configuration: {e}")

def main():
    """Main function."""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "env":
            create_env_file()
        elif command == "github":
            show_github_setup_instructions()
        elif command == "database":
            generate_database_config()
        elif command == "security":
            show_security_recommendations()
        elif command == "secret":
            print("🔑 Generated secret key:")
            print(generate_secret_key())
        elif command == "password":
            length = int(sys.argv[2]) if len(sys.argv) > 2 else 16
            print(f"🔒 Generated password ({length} chars):")
            print(generate_password(length))
        elif command == "validate":
            validate_configuration()
        else:
            print(f"Unknown command: {command}")
            show_help()
    else:
        show_help()

def show_help():
    """Show help information."""
    print("🚀 WastedTime Configuration Generator")
    print("=" * 50)
    print("Usage: python scripts/generate_config.py <command>")
    print("\nCommands:")
    print("  env       - Create .env file with secure defaults")
    print("  github    - Show GitHub OAuth setup instructions")
    print("  database  - Show database configuration examples")
    print("  security  - Show security recommendations")
    print("  secret    - Generate a secret key")
    print("  password  - Generate a password (optional length)")
    print("  validate  - Validate current configuration")
    print("\nExamples:")
    print("  python scripts/generate_config.py env")
    print("  python scripts/generate_config.py secret")
    print("  python scripts/generate_config.py password 20")
    print("  python scripts/generate_config.py validate")

if __name__ == "__main__":
    main()
