import os
from typing import List
from dotenv import load_dotenv

load_dotenv()

class Settings:
    """Application settings loaded from environment variables."""

    # GitHub OAuth settings
    GITHUB_CLIENT_ID: str = os.getenv("GITHUB_CLIENT_ID", "")
    GITHUB_CLIENT_SECRET: str = os.getenv("GITHUB_CLIENT_SECRET", "")

    # Application settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./wastedtime.db")
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = os.getenv("DEBUG", "True").lower() == "true"

    # Server settings
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))

    # File storage settings
    STORAGE_DIR: str = os.getenv("STORAGE_DIR", "storage")
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB default
    MAX_REPOSITORY_SIZE: int = int(os.getenv("MAX_REPOSITORY_SIZE", "1073741824"))  # 1GB default
    MAX_FILENAME_LENGTH: int = int(os.getenv("MAX_FILENAME_LENGTH", "255"))

    # File type restrictions
    ALLOWED_FILE_EXTENSIONS: List[str] = [
        ext.strip() for ext in os.getenv("ALLOWED_FILE_EXTENSIONS", "").split(",") if ext.strip()
    ]
    BLOCKED_FILE_EXTENSIONS: List[str] = [
        ext.strip() for ext in os.getenv(
            "BLOCKED_FILE_EXTENSIONS",
            ".exe,.bat,.cmd,.com,.scr,.pif,.vbs,.js,.jar,.app,.deb,.rpm,.dmg,.pkg"
        ).split(",") if ext.strip()
    ]

    # Session settings
    SESSION_TIMEOUT: int = int(os.getenv("SESSION_TIMEOUT", "86400"))  # 24 hours
    SESSION_COOKIE_SECURE: bool = os.getenv("SESSION_COOKIE_SECURE", "False").lower() == "true"
    SESSION_COOKIE_HTTPONLY: bool = os.getenv("SESSION_COOKIE_HTTPONLY", "True").lower() == "true"
    SESSION_COOKIE_SAMESITE: str = os.getenv("SESSION_COOKIE_SAMESITE", "Lax")

    # Rate limiting
    ENABLE_RATE_LIMITING: bool = os.getenv("ENABLE_RATE_LIMITING", "True").lower() == "true"
    RATE_LIMIT_PER_MINUTE: int = int(os.getenv("RATE_LIMIT_PER_MINUTE", "60"))
    RATE_LIMIT_PER_HOUR: int = int(os.getenv("RATE_LIMIT_PER_HOUR", "1000"))

    # GitHub API settings
    GITHUB_API_BASE_URL: str = os.getenv("GITHUB_API_BASE_URL", "https://api.github.com")
    GITHUB_API_TIMEOUT: int = int(os.getenv("GITHUB_API_TIMEOUT", "30"))
    GITHUB_API_MAX_RETRIES: int = int(os.getenv("GITHUB_API_MAX_RETRIES", "3"))

    # GitHub OAuth URLs
    GITHUB_AUTHORIZE_URL: str = "https://github.com/login/oauth/authorize"
    GITHUB_TOKEN_URL: str = "https://github.com/login/oauth/access_token"
    GITHUB_USER_URL: str = "https://api.github.com/user"

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "")
    LOG_REQUESTS: bool = os.getenv("LOG_REQUESTS", "True").lower() == "true"

    # Security settings
    FORCE_HTTPS: bool = os.getenv("FORCE_HTTPS", "False").lower() == "true"
    TRUSTED_HOSTS: List[str] = [
        host.strip() for host in os.getenv("TRUSTED_HOSTS", "localhost,127.0.0.1").split(",") if host.strip()
    ]
    CORS_ALLOWED_ORIGINS: List[str] = [
        origin.strip() for origin in os.getenv("CORS_ALLOWED_ORIGINS", "").split(",") if origin.strip()
    ]

    # Development settings
    AUTO_RELOAD: bool = os.getenv("AUTO_RELOAD", "True").lower() == "true"
    SHOW_ERROR_DETAILS: bool = os.getenv("SHOW_ERROR_DETAILS", "True").lower() == "true"
    DISABLE_AUTH: bool = os.getenv("DISABLE_AUTH", "False").lower() == "true"

    # Testing settings
    TEST_DATABASE_URL: str = os.getenv("TEST_DATABASE_URL", "sqlite:///./test.db")
    TEST_STORAGE_DIR: str = os.getenv("TEST_STORAGE_DIR", "test_storage")

    # Backup settings
    ENABLE_AUTO_BACKUP: bool = os.getenv("ENABLE_AUTO_BACKUP", "False").lower() == "true"
    BACKUP_DIR: str = os.getenv("BACKUP_DIR", "backups")
    BACKUP_RETENTION_DAYS: int = int(os.getenv("BACKUP_RETENTION_DAYS", "30"))

    # Optional integrations
    SENTRY_DSN: str = os.getenv("SENTRY_DSN", "")
    GA_TRACKING_ID: str = os.getenv("GA_TRACKING_ID", "")

    # Email settings (optional)
    SMTP_HOST: str = os.getenv("SMTP_HOST", "")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME: str = os.getenv("SMTP_USERNAME", "")
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "")
    SMTP_USE_TLS: bool = os.getenv("SMTP_USE_TLS", "True").lower() == "true"
    EMAIL_FROM: str = os.getenv("EMAIL_FROM", "<EMAIL>")

    @property
    def is_configured(self) -> bool:
        """Check if required OAuth settings are configured."""
        return bool(self.GITHUB_CLIENT_ID and self.GITHUB_CLIENT_SECRET)

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT.lower() == "development"

    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.ENVIRONMENT.lower() == "testing"

    def get_database_url(self) -> str:
        """Get the appropriate database URL based on environment."""
        if self.is_testing:
            return self.TEST_DATABASE_URL
        return self.DATABASE_URL

    def get_storage_dir(self) -> str:
        """Get the appropriate storage directory based on environment."""
        if self.is_testing:
            return self.TEST_STORAGE_DIR
        return self.STORAGE_DIR

    def validate_configuration(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []

        # Check required settings
        if not self.GITHUB_CLIENT_ID:
            issues.append("GITHUB_CLIENT_ID is not set")

        if not self.GITHUB_CLIENT_SECRET:
            issues.append("GITHUB_CLIENT_SECRET is not set")

        if self.SECRET_KEY == "your-secret-key-change-in-production":
            issues.append("SECRET_KEY is using default value - change it for security")

        if len(self.SECRET_KEY) < 32:
            issues.append("SECRET_KEY should be at least 32 characters long")

        # Production-specific checks
        if self.is_production:
            if self.DEBUG:
                issues.append("DEBUG should be False in production")

            if not self.FORCE_HTTPS:
                issues.append("FORCE_HTTPS should be True in production")

            if not self.SESSION_COOKIE_SECURE:
                issues.append("SESSION_COOKIE_SECURE should be True in production")

            if "sqlite" in self.DATABASE_URL.lower():
                issues.append("Consider using PostgreSQL instead of SQLite in production")

        # File size checks
        if self.MAX_FILE_SIZE > 104857600:  # 100MB
            issues.append("MAX_FILE_SIZE exceeds GitHub's 100MB limit")

        return issues

    def print_configuration_status(self):
        """Print configuration status and any issues."""
        print(f"🔧 WastedTime Configuration Status")
        print(f"Environment: {self.ENVIRONMENT}")
        print(f"Debug Mode: {self.DEBUG}")
        print(f"OAuth Configured: {self.is_configured}")

        issues = self.validate_configuration()
        if issues:
            print(f"\n⚠️  Configuration Issues:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ Configuration looks good!")

settings = Settings()