from fastapi import <PERSON>AP<PERSON>, UploadFile, File
from fastapi.responses import JSONResponse
import shutil
from pathlib import Path
from typing import Optional
import os

app = FastAPI(title="File Upload API")

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

@app.get("/")
async def root():
    """Root endpoint that returns a welcome message"""
    return {"message": "Welcome to the File Upload API"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    Upload a file and return its contents
    """
    try:
        # Read the contents of the file
        contents = await file.read()
        
        # Save the file
        file_path = UPLOAD_DIR / file.filename
        with open(file_path, "wb") as f:
            f.write(contents)
        
        # Return the contents as text if possible, otherwise return binary data info
        try:
            text_contents = contents.decode('utf-8')
            return {
                "filename": file.filename,
                "content_type": file.content_type,
                "contents": text_contents
            }
        except UnicodeDecodeError:
            return {
                "filename": file.filename,
                "content_type": file.content_type,
                "size_bytes": len(contents),
                "message": "File contents are binary and cannot be displayed as text"
            }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while processing the file: {str(e)}"}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 