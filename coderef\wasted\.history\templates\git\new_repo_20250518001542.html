{% extends "git/base.html" %}

{% block title %}Create New Repository{% endblock %}

{% block content %}
<div class="max-w-md mx-auto bg-white shadow-md rounded-lg p-8">
    <h1 class="text-2xl font-bold mb-6 text-center text-gray-800">Create a New Repository</h1>
    
    <form id="newRepoForm" class="space-y-4">
        <div>
            <label for="owner" class="block text-sm font-medium text-gray-700 mb-2">
                Owner
            </label>
            <input 
                type="text" 
                id="owner" 
                name="owner" 
                required 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter owner name"
            >
        </div>
        
        <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Repository Name
            </label>
            <input 
                type="text" 
                id="name" 
                name="name" 
                required 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Choose a repository name"
            >
        </div>
        
        <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Description (Optional)
            </label>
            <textarea 
                id="description" 
                name="description" 
                rows="3" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Add a description to your repository"
            ></textarea>
        </div>
        
        <div class="flex items-center space-x-2 mb-4">
            <input 
                type="checkbox" 
                id="private" 
                name="private" 
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <label for="private" class="text-sm text-gray-700">
                Make this repository private
            </label>
        </div>
        
        <button 
            type="submit" 
            class="w-full bg-green-500 text-white py-2 rounded-md hover:bg-green-600 transition duration-300 flex items-center justify-center"
        >
            <i class="fas fa-plus mr-2"></i>Create Repository
        </button>
    </form>
    
    <div id="errorMessage" class="mt-4 text-red-500 text-center hidden"></div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('newRepoForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.classList.add('hidden');
    
    const owner = document.getElementById('owner').value.trim();
    const name = document.getElementById('name').value.trim();
    
    if (!owner || !name) {
        errorMessage.textContent = 'Owner and Repository Name are required';
        errorMessage.classList.remove('hidden');
        return;
    }
    
    try {
        const response = await fetch('/git/new', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `owner=${encodeURIComponent(owner)}&name=${encodeURIComponent(name)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.href = `/git/${owner}/${name}`;
        } else {
            errorMessage.textContent = data.message || 'Error creating repository';
            errorMessage.classList.remove('hidden');
        }
    } catch (error) {
        errorMessage.textContent = 'Network error. Please try again.';
        errorMessage.classList.remove('hidden');
    }
});
</script>
{% endblock %} 