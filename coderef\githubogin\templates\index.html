{% extends "base.html" %}

{% block title %}Home - GitHub OAuth App{% endblock %}

{% block content %}
<div class="text-center">
    {% if user %}
        <div class="bg-white shadow rounded-lg p-8 max-w-md mx-auto">
            <div class="text-center">
                {% if user.avatar_url %}
                    <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="w-24 h-24 rounded-full mx-auto mb-4">
                {% endif %}
                <h1 class="text-2xl font-bold text-gray-800 mb-2">Welcome, {{ user.name or user.username }}!</h1>
                <p class="text-gray-600 mb-4">@{{ user.username }}</p>
                {% if user.email %}
                    <p class="text-gray-600 mb-4">
                        <i class="fas fa-envelope mr-2"></i>{{ user.email }}
                    </p>
                {% endif %}
                <p class="text-sm text-gray-500 mb-6">
                    Member since {{ user.created_at.strftime('%B %d, %Y') }}
                </p>
                <div class="space-y-3">
                    <a href="/profile" class="block w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md transition duration-300">
                        <i class="fas fa-user mr-2"></i>View Profile
                    </a>
                    <a href="/logout" class="block w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition duration-300">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    {% else %}
        <div class="bg-white shadow rounded-lg p-8 max-w-md mx-auto">
            <div class="text-center">
                <i class="fab fa-github text-6xl text-gray-400 mb-6"></i>
                <h1 class="text-3xl font-bold text-gray-800 mb-4">Welcome to GitHub OAuth</h1>
                <p class="text-gray-600 mb-8">Sign in with your GitHub account to get started</p>
                <a href="/login" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900 transition duration-300">
                    <i class="fab fa-github mr-3"></i>
                    Sign in with GitHub
                </a>
            </div>
        </div>
    {% endif %}
</div>

{% if not user %}
<div class="mt-12 text-center">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Features</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow">
            <i class="fas fa-shield-alt text-3xl text-green-500 mb-4"></i>
            <h3 class="text-lg font-semibold mb-2">Secure Authentication</h3>
            <p class="text-gray-600">OAuth 2.0 with GitHub for secure login</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
            <i class="fas fa-user text-3xl text-blue-500 mb-4"></i>
            <h3 class="text-lg font-semibold mb-2">Profile Management</h3>
            <p class="text-gray-600">Access your GitHub profile information</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
            <i class="fas fa-database text-3xl text-purple-500 mb-4"></i>
            <h3 class="text-lg font-semibold mb-2">Data Persistence</h3>
            <p class="text-gray-600">User data stored securely with SQLAlchemy</p>
        </div>
    </div>
</div>
{% endif %}
{% endblock %} 