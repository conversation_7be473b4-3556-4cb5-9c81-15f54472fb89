# WastedTime Setup Guide

This guide will help you set up the WastedTime application for development or production use.

## Prerequisites

- Python 3.8 or higher
- Git
- GitHub account
- Text editor or IDE

## Quick Start (Development)

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd wastedtime
```

### 2. Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Set Up GitHub OAuth App

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the details:
   - **Application name**: WastedTime (or your preferred name)
   - **Homepage URL**: `http://localhost:8000`
   - **Application description**: Virtual file system with GitHub integration
   - **Authorization callback URL**: `http://localhost:8000/auth/callback`
4. Click "Register application"
5. Copy the **Client ID** and **Client Secret**

### 5. Configure Environment

```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your settings
# At minimum, set these values:
GITHUB_CLIENT_ID=your_client_id_here
GITHUB_CLIENT_SECRET=your_client_secret_here
SECRET_KEY=your-secret-key-here
```

**Generate a secure secret key:**
```bash
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### 6. Run the Application

```bash
python run.py
```

The application will be available at `http://localhost:8000`

## Environment Configuration

### Required Settings

These settings must be configured for the application to work:

```env
# GitHub OAuth (Required)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Security (Required)
SECRET_KEY=your-secure-secret-key
```

### Common Development Settings

```env
# Development mode
ENVIRONMENT=development
DEBUG=True
AUTO_RELOAD=True
SHOW_ERROR_DETAILS=True

# Database (SQLite for development)
DATABASE_URL=sqlite:///./wastedtime.db

# File storage
STORAGE_DIR=storage
MAX_FILE_SIZE=10485760  # 10MB
```

### Production Settings

```env
# Production mode
ENVIRONMENT=production
DEBUG=False
AUTO_RELOAD=False
SHOW_ERROR_DETAILS=False

# Security
FORCE_HTTPS=True
SESSION_COOKIE_SECURE=True
TRUSTED_HOSTS=yourdomain.com,www.yourdomain.com

# Database (PostgreSQL recommended)
DATABASE_URL=postgresql://user:password@localhost:5432/wastedtime

# Logging
LOG_LEVEL=WARNING
LOG_FILE=/var/log/wastedtime/app.log
```

## Database Setup

### SQLite (Development)
No additional setup required. The database file will be created automatically.

### PostgreSQL (Production)

1. Install PostgreSQL
2. Create database and user:
```sql
CREATE DATABASE wastedtime;
CREATE USER wastedtime_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE wastedtime TO wastedtime_user;
```
3. Update DATABASE_URL in .env

### MySQL (Alternative)

1. Install MySQL
2. Create database and user:
```sql
CREATE DATABASE wastedtime;
CREATE USER 'wastedtime_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON wastedtime.* TO 'wastedtime_user'@'localhost';
```
3. Update DATABASE_URL in .env

## File Storage Configuration

### Local Storage (Default)
Files are stored in the `storage/` directory. Ensure proper permissions:

```bash
mkdir -p storage
chmod 755 storage
```

### Cloud Storage (Production)
For production, consider using cloud storage services:
- AWS S3
- Google Cloud Storage
- Azure Blob Storage

## Security Configuration

### File Type Restrictions

Configure allowed/blocked file extensions:

```env
# Allow only specific types (empty = allow all)
ALLOWED_FILE_EXTENSIONS=.txt,.md,.py,.js,.html,.css,.json

# Block dangerous file types
BLOCKED_FILE_EXTENSIONS=.exe,.bat,.cmd,.com,.scr,.pif,.vbs
```

### Rate Limiting

Configure API rate limits:

```env
ENABLE_RATE_LIMITING=True
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
```

## Testing

### Run Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest

# Run with coverage
pytest --cov=app tests/
```

### Test Configuration

Tests use separate configuration:

```env
TEST_DATABASE_URL=sqlite:///./test.db
TEST_STORAGE_DIR=test_storage
```

## Deployment

### Docker Deployment

Create `Dockerfile`:
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "run.py"]
```

### Systemd Service (Linux)

Create `/etc/systemd/system/wastedtime.service`:
```ini
[Unit]
Description=WastedTime Application
After=network.target

[Service]
Type=simple
User=wastedtime
WorkingDirectory=/opt/wastedtime
Environment=PATH=/opt/wastedtime/venv/bin
ExecStart=/opt/wastedtime/venv/bin/python run.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## Troubleshooting

### Common Issues

1. **OAuth Error**: Check GitHub OAuth app settings and callback URL
2. **Database Error**: Verify database connection and permissions
3. **File Upload Error**: Check storage directory permissions
4. **Import Error**: Ensure all dependencies are installed

### Debug Mode

Enable debug mode for detailed error information:

```env
DEBUG=True
SHOW_ERROR_DETAILS=True
LOG_LEVEL=DEBUG
```

### Logs

Check application logs:
- Console output (development)
- Log file (production): Set LOG_FILE in .env

## Support

For issues and questions:
1. Check this setup guide
2. Review the main README.md
3. Check the GitHub issues
4. Create a new issue with detailed information

## Security Notes

- Never commit `.env` file to version control
- Use strong, unique secret keys
- Enable HTTPS in production
- Regularly update dependencies
- Monitor logs for suspicious activity
