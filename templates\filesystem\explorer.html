{% extends "base.html" %}

{% block title %}File Explorer - {{ repository.name }} - WastedTime{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-folder-open"></i> {{ repository.name }}
    </h1>
    <div class="d-flex gap-2">
        <button class="btn btn-success" onclick="showUploadModal()">
            <i class="fas fa-upload"></i> Upload File
        </button>
        <button class="btn btn-secondary" onclick="showCreateFolderModal()">
            <i class="fas fa-folder-plus"></i> New Folder
        </button>
        <a href="/dashboard" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Dashboard
        </a>
    </div>
</div>

{% if error %}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle"></i> {{ error }}
</div>
{% endif %}

<!-- File Explorer -->
<div class="file-explorer">
    <!-- Breadcrumb Navigation -->
    <nav>
        <ol class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li class="breadcrumb-item">
                {% if loop.last %}
                {{ breadcrumb.name }}
                {% else %}
                <a href="/fs/?path={{ breadcrumb.path }}">{{ breadcrumb.name }}</a>
                {% endif %}
            </li>
            {% endfor %}
        </ol>
    </nav>
    
    <!-- File List -->
    {% if contents %}
    <ul class="file-list">
        {% for item in contents %}
        <li class="file-item">
            <i class="{{ get_file_icon(item.mime_type, item.name.split('.')[-1] if '.' in item.name else None) if item.type == 'file' else 'fas fa-folder' }} file-icon"></i>
            
            {% if item.type == 'folder' %}
            <a href="/fs/?path={{ item.path }}" class="file-name">{{ item.name }}</a>
            {% else %}
            <span class="file-name">{{ item.name }}</span>
            {% endif %}
            
            <span class="file-size">
                {% if item.type == 'file' and item.size %}
                {{ format_file_size(item.size) }}
                {% endif %}
            </span>
            
            <div class="file-actions">
                {% if item.type == 'folder' %}
                <button class="btn btn-sm btn-danger" onclick="deleteFolder({{ item.id }}, '{{ item.name }}')">
                    <i class="fas fa-trash"></i>
                </button>
                {% else %}
                <button class="btn btn-sm btn-danger" onclick="deleteFile({{ item.id }}, '{{ item.name }}')">
                    <i class="fas fa-trash"></i>
                </button>
                {% endif %}
            </div>
        </li>
        {% endfor %}
    </ul>
    {% else %}
    <div class="text-center" style="padding: 3rem;">
        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
        <h5>Empty Directory</h5>
        <p class="text-muted">This directory is empty. Upload files or create folders to get started.</p>
        <div class="d-flex gap-2 justify-content-center">
            <button class="btn btn-primary" onclick="showUploadModal()">
                <i class="fas fa-upload"></i> Upload File
            </button>
            <button class="btn btn-secondary" onclick="showCreateFolderModal()">
                <i class="fas fa-folder-plus"></i> Create Folder
            </button>
        </div>
    </div>
    {% endif %}
</div>

<!-- Upload Modal -->
<div id="uploadModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; min-width: 400px;">
        <h4>Upload File</h4>
        <form id="uploadForm">
            <div class="form-group">
                <label class="form-label">Select File:</label>
                <input type="file" id="fileInput" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Upload to:</label>
                <input type="text" id="uploadPath" class="form-control" value="{{ current_path }}" readonly>
            </div>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Upload</button>
                <button type="button" class="btn btn-secondary" onclick="hideUploadModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<!-- Create Folder Modal -->
<div id="createFolderModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; min-width: 400px;">
        <h4>Create Folder</h4>
        <form id="createFolderForm">
            <div class="form-group">
                <label class="form-label">Folder Name:</label>
                <input type="text" id="folderName" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Create in:</label>
                <input type="text" id="folderPath" class="form-control" value="{{ current_path }}" readonly>
            </div>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Create</button>
                <button type="button" class="btn btn-secondary" onclick="hideCreateFolderModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; min-width: 400px;">
        <h4 class="text-danger">
            <i class="fas fa-exclamation-triangle"></i> Confirm Delete
        </h4>
        <p id="deleteMessage"></p>
        <div class="d-flex gap-2">
            <button id="confirmDeleteBtn" class="btn btn-danger">
                <i class="fas fa-trash"></i> Delete
            </button>
            <button class="btn btn-secondary" onclick="hideDeleteModal()">Cancel</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Modal functions
function showUploadModal() {
    document.getElementById('uploadModal').style.display = 'block';
}

function hideUploadModal() {
    document.getElementById('uploadModal').style.display = 'none';
    document.getElementById('uploadForm').reset();
}

function showCreateFolderModal() {
    document.getElementById('createFolderModal').style.display = 'block';
}

function hideCreateFolderModal() {
    document.getElementById('createFolderModal').style.display = 'none';
    document.getElementById('createFolderForm').reset();
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

// Upload form handler
document.getElementById('uploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const fileInput = document.getElementById('fileInput');
    const pathInput = document.getElementById('uploadPath');
    
    if (!fileInput.files[0]) {
        showAlert('Please select a file', 'danger');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('path', pathInput.value);
    
    try {
        const response = await axios.post('/fs/upload-file', formData);
        showAlert(response.data.message, 'success');
        hideUploadModal();
        window.location.reload();
    } catch (error) {
        handleApiError(error);
    }
});

// Create folder form handler
document.getElementById('createFolderForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const nameInput = document.getElementById('folderName');
    const pathInput = document.getElementById('folderPath');
    
    const formData = new FormData();
    formData.append('name', nameInput.value);
    formData.append('path', pathInput.value);
    
    try {
        const response = await axios.post('/fs/create-folder', formData);
        showAlert(response.data.message, 'success');
        hideCreateFolderModal();
        window.location.reload();
    } catch (error) {
        handleApiError(error);
    }
});

// Delete functions
function deleteFolder(folderId, folderName) {
    document.getElementById('deleteMessage').textContent = 
        `Are you sure you want to delete the folder "${folderName}" and all its contents?`;
    document.getElementById('confirmDeleteBtn').onclick = () => confirmDeleteFolder(folderId);
    document.getElementById('deleteModal').style.display = 'block';
}

function deleteFile(fileId, fileName) {
    document.getElementById('deleteMessage').textContent = 
        `Are you sure you want to delete the file "${fileName}"?`;
    document.getElementById('confirmDeleteBtn').onclick = () => confirmDeleteFile(fileId);
    document.getElementById('deleteModal').style.display = 'block';
}

async function confirmDeleteFolder(folderId) {
    try {
        const response = await axios.delete(`/fs/folder/${folderId}`);
        showAlert(response.data.message, 'success');
        hideDeleteModal();
        window.location.reload();
    } catch (error) {
        handleApiError(error);
        hideDeleteModal();
    }
}

async function confirmDeleteFile(fileId) {
    try {
        const response = await axios.delete(`/fs/file/${fileId}`);
        showAlert(response.data.message, 'success');
        hideDeleteModal();
        window.location.reload();
    } catch (error) {
        handleApiError(error);
        hideDeleteModal();
    }
}
</script>
{% endblock %}
