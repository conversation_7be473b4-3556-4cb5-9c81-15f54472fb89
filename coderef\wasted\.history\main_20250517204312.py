from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Request
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pathlib import Path
import os
import google.generativeai as genai
from dotenv import load_dotenv
from PyPDF2 import PdfReader
import docx
import json

# Load environment variables
load_dotenv()

# Configure Gemini API
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
genai.configure(api_key=GOOGLE_API_KEY)

app = FastAPI(title="File Upload API")

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# Create templates directory if it doesn't exist
TEMPLATES_DIR = Path("templates")
TEMPLATES_DIR.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

async def extract_docx_text(file_path: Path) -> str:
    """Extract text from DOCX file"""
    try:
        doc = docx.Document(file_path)
        return "\n".join([paragraph.text for paragraph in doc.paragraphs])
    except Exception as e:
        return f"Error extracting DOCX text: {str(e)}"

async def extract_pdf_text(file_path: Path) -> str:
    """Extract text from PDF file"""
    try:
        reader = PdfReader(file_path)
        text = []
        for page in reader.pages:
            text.append(page.extract_text())
        return "\n".join(text)
    except Exception as e:
        return f"Error extracting PDF text: {str(e)}"

async def analyze_with_gemini(content: str, file_type: str, filename: str) -> dict:
    """Analyze file contents using Gemini API and return structured JSON"""
    try:
        # Use Gemini 2.5 Flash Preview model
        model = genai.GenerativeModel('gemini-2.0-flash') Gemini 2.0 Flash
        
        # Configure for structured output
        prompt = f"""Analyze this {file_type} file named {filename} and provide a structured analysis.
        
        Content to analyze:
        {content}
        
        Provide the response in the following JSON structure:
        {{
            "summary": "A concise 50-word summary of the content",
            "key_points": ["List of 3-5 main points"],
            "document_type": "Type of document (e.g., report, letter, article)",
            "word_count": "Approximate word count",
            "tone": "Overall tone of the document (formal, informal, technical, etc.)"
        }}

        Return ONLY the JSON object, no additional text.
        """
        
        response = model.generate_content(prompt, generation_config={
            "temperature": 0.3,
            "top_p": 0.8,
            "top_k": 40
        })
        
        # Extract JSON from response
        try:
            # First try to parse the response text directly
            result = json.loads(response.text)
        except json.JSONDecodeError:
            # If direct parsing fails, try to extract JSON from the text
            text = response.text
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            if start_idx != -1 and end_idx != 0:
                json_str = text[start_idx:end_idx]
                result = json.loads(json_str)
            else:
                raise ValueError("Could not extract valid JSON from response")
        
        return result
        
    except Exception as e:
        return {
            "error": f"Error analyzing content with Gemini: {str(e)}",
            "summary": "Error generating summary",
            "key_points": [],
            "document_type": "unknown",
            "word_count": 0,
            "tone": "unknown"
        }

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Render the upload form"""
    return templates.TemplateResponse("upload.html", {"request": request})

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a file and return its contents"""
    try:
        contents = await file.read()
        
        # Save the file
        file_path = UPLOAD_DIR / file.filename
        with open(file_path, "wb") as f:
            f.write(contents)
        
        # Get file extension
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
        
        if file_ext in ['pdf', 'docx']:
            return {
                "filename": file.filename,
                "content_type": f"application/{file_ext}",
                "message": "File uploaded successfully"
            }
        else:
            return {
                "filename": file.filename,
                "content_type": "unsupported",
                "message": "File type not supported. Please upload PDF or DOCX files only."
            }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while processing the file: {str(e)}"}
        )

@app.post("/analyze")
async def analyze_file(file: UploadFile = File(...)):
    """Upload a file and analyze its contents using Gemini"""
    try:
        contents = await file.read()
        file_path = UPLOAD_DIR / file.filename
        
        # Save the file temporarily
        with open(file_path, "wb") as f:
            f.write(contents)

        # Get file extension
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
        
        # Only process PDF and DOCX files
        if file_ext not in ['pdf', 'docx']:
            return JSONResponse(
                status_code=400,
                content={"message": "Only PDF and DOCX files are supported"}
            )
        
        # Extract text based on file type
        if file_ext == 'pdf':
            text_content = await extract_pdf_text(file_path)
            file_type = "PDF document"
        else:  # docx
            text_content = await extract_docx_text(file_path)
            file_type = "Word document"
        
        # Check if text extraction was successful
        if text_content.startswith("Error"):
            return JSONResponse(
                status_code=500,
                content={"message": text_content}
            )
        
        # Analyze with Gemini
        analysis = await analyze_with_gemini(text_content, file_type, file.filename)
        
        return {
            "filename": file.filename,
            "file_type": file_type,
            "analysis": analysis
        }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while analyzing the file: {str(e)}"}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 