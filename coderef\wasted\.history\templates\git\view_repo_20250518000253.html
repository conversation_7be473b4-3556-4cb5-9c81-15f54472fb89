{% extends "git/base.html" %}

{% block title %}{{ repo_name }} - Repository{% endblock %}

{% block content %}
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold flex items-center">
                <i class="fas fa-book mr-3"></i>
                <span class="text-gray-600">{{ owner }}/</span>{{ repo_name }}
            </h1>
            {% if metadata.forked_from %}
            <p class="text-gray-600 mt-2">
                <i class="fas fa-code-branch mr-1"></i>
                Forked from {{ metadata.forked_from.owner }}/{{ metadata.forked_from.name }}
            </p>
            {% endif %}
        </div>
        <button onclick="forkRepository()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
            <i class="fas fa-code-branch mr-2"></i>Fork
        </button>
    </div>
</div>

<div class="bg-white rounded-lg shadow-md">
    <!-- Repository Actions -->
    <div class="border-b p-4">
        <div class="flex space-x-4">
            <button onclick="showUploadModal()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                <i class="fas fa-upload mr-2"></i>Upload Files
            </button>
            <button onclick="showNewDirModal()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                <i class="fas fa-folder-plus mr-2"></i>New Directory
            </button>
        </div>
    </div>

    <!-- File Browser -->
    <div class="p-4">
        <div class="mb-4 text-sm breadcrumbs">
            <a href="/git/{{ owner }}/{{ repo_name }}" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-home"></i>
            </a>
            {% for part in current_path.split('/') if part %}
            <span class="mx-2">/</span>
            <a href="/git/{{ owner }}/{{ repo_name }}?path={{ part }}" class="text-blue-600 hover:text-blue-800">
                {{ part }}
            </a>
            {% endfor %}
        </div>

        <table class="w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in structure %}
                    {% if item.path == current_path %}
                        {% for dir in item.dirs %}
                        <tr>
                            <td class="px-6 py-4">
                                <a href="?path={{ item.path + '/' + dir if item.path else dir }}" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-folder text-yellow-500 mr-2"></i>{{ dir }}
                                </a>
                            </td>
                            <td class="px-6 py-4 text-gray-500">Directory</td>
                            <td class="px-6 py-4">
                                <button class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                        {% for file in item.files %}
                        <tr>
                            <td class="px-6 py-4">
                                <span>
                                    <i class="fas fa-file text-gray-500 mr-2"></i>{{ file }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-gray-500">File</td>
                            <td class="px-6 py-4">
                                <button class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Upload Modal -->
<div id="uploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold mb-4">Upload Files</h2>
            <form id="uploadForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">Select Files</label>
                    <input type="file" id="files" name="file" multiple class="w-full border rounded px-3 py-2">
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeUploadModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Cancel
                    </button>
                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Upload
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- New Directory Modal -->
<div id="newDirModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold mb-4">Create New Directory</h2>
            <form id="newDirForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">Directory Name</label>
                    <input type="text" id="dirName" name="path" class="w-full border rounded px-3 py-2" required>
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeNewDirModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Cancel
                    </button>
                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Create
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Fork Modal -->
<div id="forkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold mb-4">Fork Repository</h2>
            <form id="forkForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">New Owner Name</label>
                    <input type="text" id="newOwner" name="new_owner" class="w-full border rounded px-3 py-2" required>
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeForkModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Cancel
                    </button>
                    <button type="submit" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Fork
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const currentPath = new URLSearchParams(window.location.search).get('path') || '';

function showUploadModal() {
    document.getElementById('uploadModal').classList.remove('hidden');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('uploadForm').reset();
}

function showNewDirModal() {
    document.getElementById('newDirModal').classList.remove('hidden');
}

function closeNewDirModal() {
    document.getElementById('newDirModal').classList.add('hidden');
    document.getElementById('newDirForm').reset();
}

function forkRepository() {
    document.getElementById('forkModal').classList.remove('hidden');
}

function closeForkModal() {
    document.getElementById('forkModal').classList.add('hidden');
    document.getElementById('forkForm').reset();
}

// Handle file upload
document.getElementById('uploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData();
    const fileInput = document.getElementById('files');
    
    for (const file of fileInput.files) {
        formData.append('file', file);
    }
    formData.append('path', currentPath);
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/upload`, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error uploading files: ' + data.message);
        }
    } catch (error) {
        alert('Error uploading files: ' + error.message);
    }
});

// Handle new directory creation
document.getElementById('newDirForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const dirName = document.getElementById('dirName').value;
    const path = currentPath ? `${currentPath}/${dirName}` : dirName;
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/mkdir`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `path=${encodeURIComponent(path)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error creating directory: ' + data.message);
        }
    } catch (error) {
        alert('Error creating directory: ' + error.message);
    }
});

// Handle repository forking
document.getElementById('forkForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const newOwner = document.getElementById('newOwner').value;
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/fork`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `new_owner=${encodeURIComponent(newOwner)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.href = `/git/${newOwner}/${data.new_repo.name}`;
        } else {
            alert('Error forking repository: ' + data.message);
        }
    } catch (error) {
        alert('Error forking repository: ' + error.message);
    }
});
</script>
{% endblock %} 