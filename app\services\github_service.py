import httpx
import base64
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class GitHubService:
    """
    Service for GitHub API operations.

    NOTE: Currently uses the user's own access token for repository operations.
    In the future, we will implement a two-phase approach:
    1. Phase 1 (Current): Use user's basic token for initial repository creation
    2. Phase 2 (Future): Request additional 'repo' permissions when needed

    This allows users to start with minimal permissions and only grant repository
    access when they actually want to sync with GitHub.
    """

    def __init__(self, access_token: str):
        self.access_token = access_token
        self.base_url = "https://api.github.com"
        self.headers = {
            'Authorization': f'token {access_token}',
            'Accept': 'application/vnd.github.v3+json'
        }
    
    async def check_repository_permissions(self) -> bool:
        """
        Check if the current token has repository creation permissions.
        Returns True if user can create repositories, False otherwise.
        """
        async with httpx.AsyncClient() as client:
            # Try to get user's repositories to test permissions
            response = await client.get(
                f"{self.base_url}/user/repos?per_page=1",
                headers=self.headers
            )

            # If we can list repos, we likely have repo permissions
            # Status 200 = has permissions, 403/401 = insufficient permissions
            return response.status_code == 200

    async def create_repository(self, name: str, description: str = "", private: bool = False) -> Dict[str, Any]:
        """
        Create a new GitHub repository.

        NOTE: This requires 'repo' scope permissions. If the user hasn't granted
        repository permissions yet, this will fail with a 403/401 error.
        In the future, we'll implement a flow to request additional permissions.
        """
        # Check if we have repository permissions
        has_permissions = await self.check_repository_permissions()
        if not has_permissions:
            raise Exception(
                "Insufficient permissions to create repositories. "
                "Repository permissions will be requested when needed."
            )

        data = {
            "name": name,
            "description": description,
            "private": private,
            "auto_init": True  # Create with README.md
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/user/repos",
                headers=self.headers,
                json=data
            )

            if response.status_code == 201:
                logger.info(f"Successfully created repository: {name}")
                return response.json()
            elif response.status_code in [401, 403]:
                raise Exception(
                    "Insufficient permissions to create repositories. "
                    "Please grant repository access to sync with GitHub."
                )
            else:
                logger.error(f"Failed to create repository: {response.text}")
                response.raise_for_status()
    
    async def get_user_repositories(self) -> List[Dict[str, Any]]:
        """Get all repositories for the authenticated user."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/user/repos?sort=created&direction=desc",
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get repositories: {response.text}")
                return []
    
    async def upload_file(self, repo_name: str, file_path: str, content: bytes, 
                         commit_message: str = "Upload file") -> Dict[str, Any]:
        """Upload a file to a GitHub repository."""
        # Get username first
        username = await self._get_username()
        if not username:
            raise Exception("Could not get username")
        
        # Encode file content to base64
        encoded_content = base64.b64encode(content).decode('utf-8')
        
        data = {
            "message": commit_message,
            "content": encoded_content
        }
        
        async with httpx.AsyncClient() as client:
            # First, check if file exists to get its SHA (for updates)
            get_response = await client.get(
                f"{self.base_url}/repos/{username}/{repo_name}/contents/{file_path}",
                headers=self.headers
            )
            
            if get_response.status_code == 200:
                # File exists, add SHA for update
                file_info = get_response.json()
                data["sha"] = file_info["sha"]
                logger.info(f"Updating existing file: {file_path}")
            else:
                logger.info(f"Creating new file: {file_path}")
            
            # Upload or update the file
            response = await client.put(
                f"{self.base_url}/repos/{username}/{repo_name}/contents/{file_path}",
                headers=self.headers,
                json=data
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"Successfully uploaded file: {file_path}")
                return response.json()
            else:
                logger.error(f"Failed to upload file: {response.text}")
                response.raise_for_status()
    
    async def delete_file(self, repo_name: str, file_path: str, 
                         commit_message: str = "Delete file") -> Dict[str, Any]:
        """Delete a file from a GitHub repository."""
        username = await self._get_username()
        if not username:
            raise Exception("Could not get username")
        
        async with httpx.AsyncClient() as client:
            # Get file SHA first
            get_response = await client.get(
                f"{self.base_url}/repos/{username}/{repo_name}/contents/{file_path}",
                headers=self.headers
            )
            
            if get_response.status_code != 200:
                raise Exception(f"File not found: {file_path}")
            
            file_info = get_response.json()
            
            data = {
                "message": commit_message,
                "sha": file_info["sha"]
            }
            
            # Delete the file
            response = await client.delete(
                f"{self.base_url}/repos/{username}/{repo_name}/contents/{file_path}",
                headers=self.headers,
                json=data
            )
            
            if response.status_code == 200:
                logger.info(f"Successfully deleted file: {file_path}")
                return response.json()
            else:
                logger.error(f"Failed to delete file: {response.text}")
                response.raise_for_status()
    
    async def _get_username(self) -> str:
        """Get the authenticated user's username."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/user",
                headers=self.headers
            )
            
            if response.status_code == 200:
                return response.json()['login']
            return None
