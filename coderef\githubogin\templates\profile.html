{% extends "base.html" %}

{% block title %}Profile - GitHub OAuth App{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white shadow rounded-lg p-8">
        <div class="text-center mb-8">
            {% if user.avatar_url %}
                <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="w-32 h-32 rounded-full mx-auto mb-4">
            {% endif %}
            <h1 class="text-3xl font-bold text-gray-800">{{ user.name or user.username }}</h1>
            <p class="text-xl text-gray-600">@{{ user.username }}</p>
        </div>

        <div class="space-y-6">
            <div class="border-t pt-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Profile Information</h2>
                <div class="grid grid-cols-1 gap-4">
                    <div class="flex items-center">
                        <i class="fas fa-id-badge text-gray-400 w-6 mr-3"></i>
                        <div>
                            <span class="text-sm text-gray-500">GitHub ID</span>
                            <p class="font-medium">{{ user.github_id }}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <i class="fas fa-user text-gray-400 w-6 mr-3"></i>
                        <div>
                            <span class="text-sm text-gray-500">Username</span>
                            <p class="font-medium">{{ user.username }}</p>
                        </div>
                    </div>

                    {% if user.name %}
                    <div class="flex items-center">
                        <i class="fas fa-signature text-gray-400 w-6 mr-3"></i>
                        <div>
                            <span class="text-sm text-gray-500">Full Name</span>
                            <p class="font-medium">{{ user.name }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if user.email %}
                    <div class="flex items-center">
                        <i class="fas fa-envelope text-gray-400 w-6 mr-3"></i>
                        <div>
                            <span class="text-sm text-gray-500">Email</span>
                            <p class="font-medium">{{ user.email }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <div class="flex items-center">
                        <i class="fas fa-calendar text-gray-400 w-6 mr-3"></i>
                        <div>
                            <span class="text-sm text-gray-500">Member Since</span>
                            <p class="font-medium">{{ user.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <i class="fas fa-clock text-gray-400 w-6 mr-3"></i>
                        <div>
                            <span class="text-sm text-gray-500">Last Updated</span>
                            <p class="font-medium">{{ user.updated_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t pt-6">
                <div class="flex space-x-4">
                    <a href="/" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-center py-2 px-4 rounded-md transition duration-300">
                        <i class="fas fa-home mr-2"></i>Back to Home
                    </a>
                    <a href="https://github.com/{{ user.username }}" target="_blank" class="flex-1 bg-gray-800 hover:bg-gray-900 text-white text-center py-2 px-4 rounded-md transition duration-300">
                        <i class="fab fa-github mr-2"></i>View on GitHub
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 