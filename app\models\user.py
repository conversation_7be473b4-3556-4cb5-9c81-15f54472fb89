from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import BaseModel

class User(BaseModel):
    """User model for storing GitHub user information."""
    
    __tablename__ = "users"
    
    github_id = Column(Integer, unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    email = Column(String(255), nullable=True)
    name = Column(String(255), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    access_token = Column(String(1000), nullable=True)  # Store GitHub access token
    
    # Relationships
    repository = relationship("Repository", back_populates="user", uselist=False, cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', github_id={self.github_id})>"
    
    @property
    def display_name(self):
        """Return the display name (name if available, otherwise username)."""
        return self.name or self.username
    
    def has_repository(self):
        """Check if user has a repository."""
        return self.repository is not None
