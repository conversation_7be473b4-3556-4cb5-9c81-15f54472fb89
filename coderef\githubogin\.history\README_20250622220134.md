# FastAPI GitHub OAuth Application

A modern, secure web application built with FastAPI that implements GitHub OAuth authentication, SQLAlchemy for database management, and Jinja2 for templating.

## Features

✨ **Modern Authentication**
- GitHub OAuth 2.0 integration
- Secure session management
- Automatic user profile updates

🗄️ **Database Management**
- SQLAlchemy ORM with SQLite
- User model with GitHub profile data
- Automatic database initialization

🎨 **Beautiful UI**
- Responsive design with Tailwind CSS
- Modern card-based layout
- FontAwesome icons
- Dark/light theme support

🔒 **Security & Best Practices**
- Environment-based configuration
- Secure session handling
- Error handling and logging
- Input validation

## Tech Stack

- **Backend**: FastAPI, Python 3.8+
- **Authentication**: Authlib with GitHub OAuth
- **Database**: SQLAlchemy with SQLite
- **Templating**: Jinja2
- **Styling**: Tailwind CSS, FontAwesome
- **HTTP Client**: HTTPX for async requests

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <your-repo-url>
cd githubogin
pip install -r requirements.txt
```

### 2. GitHub OAuth Setup

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the application details:
   - **Application name**: Your App Name
   - **Homepage URL**: `http://localhost:8000`
   - **Authorization callback URL**: `http://localhost:8000/auth/callback`
4. Copy your Client ID and Client Secret

### 3. Environment Configuration

```bash
cp .env.example .env
```

Edit `.env` with your GitHub OAuth credentials:

```env
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here
SECRET_KEY=your_secret_key_here_for_session_signing
DATABASE_URL=sqlite:///./app.db
```

### 4. Run the Application

```bash
# Development mode
uvicorn app.main:app --reload

# Or using Python
python -m app.main
```

Visit [http://localhost:8000](http://localhost:8000) to see your app!

## Project Structure

```
githubogin/
├── app/
│   ├── __init__.py
│   ├── main.py          # FastAPI application and routes
│   ├── config.py        # Configuration and environment variables
│   ├── database.py      # SQLAlchemy models and database setup
│   └── auth.py          # OAuth authentication logic
├── templates/
│   ├── base.html        # Base template with navigation
│   ├── index.html       # Home page
│   ├── profile.html     # User profile page
│   └── error.html       # Error page
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
└── README.md           # This file
```

## API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Home page (login or dashboard) |
| `/login` | GET | Initiate GitHub OAuth |
| `/auth/callback` | GET | OAuth callback handler |
| `/logout` | GET | Logout and clear session |
| `/profile` | GET | User profile page |
| `/api/user` | GET | Get current user JSON |
| `/health` | GET | Health check endpoint |

## Configuration Options

| Variable | Description | Default |
|----------|-------------|---------|
| `GITHUB_CLIENT_ID` | GitHub OAuth Client ID | Required |
| `GITHUB_CLIENT_SECRET` | GitHub OAuth Client Secret | Required |
| `SECRET_KEY` | Session signing key | Change in production |
| `DATABASE_URL` | Database connection string | sqlite:///./app.db |

## Development

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

### Code Quality
```bash
# Format code
black app/

# Lint code
flake8 app/

# Type checking
mypy app/
```

## Deployment

### Docker (Recommended)
```bash
# Build image
docker build -t github-oauth-app .

# Run container
docker run -p 8000:8000 --env-file .env github-oauth-app
```

### Heroku
```bash
# Install Heroku CLI and login
heroku create your-app-name
heroku config:set GITHUB_CLIENT_ID=your_id
heroku config:set GITHUB_CLIENT_SECRET=your_secret
heroku config:set SECRET_KEY=your_secret_key
git push heroku main
```

## Security Considerations

- **Environment Variables**: Never commit `.env` file to version control
- **Secret Key**: Use a strong, unique secret key in production
- **HTTPS**: Always use HTTPS in production
- **Database**: Consider using PostgreSQL for production
- **Session Security**: Configure secure session cookies

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues or have questions:

1. Check the [Issues](https://github.com/your-username/githubogin/issues) page
2. Create a new issue with detailed information
3. Include logs and error messages if applicable

---

Built with ❤️ using FastAPI, SQLAlchemy, and GitHub OAuth 