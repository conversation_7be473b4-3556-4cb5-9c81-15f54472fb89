from fastapi import FastAPI
from fastapi.templating import Jinja2Templates
app = FastAPI()

@app.get("/")
def wasted():
    return {"message":  "Wasted time"}

@app.get("/login")
async def login(request: Request):
    if not settings.is_configured:
        return templates.TemplateResponse("error.html", {
            "request": request, 
            "error_message": "GitHub OAuth is not configured. Please contact the administrator."
        })