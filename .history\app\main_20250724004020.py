from fastapi import <PERSON><PERSON><PERSON>, Request, Depends
from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from starlette.middleware.sessions import SessionMiddleware
from sqlalchemy.orm import Session
from starlette.responses import RedirectResponse

from app.oauth import oauth, get_current_user
from app.config import settings
from app.database import get_db
from app.ghb_api import router as auth_router
from app.models import User  # Import to ensure model is registered

app = FastAPI()
app.add_middleware(SessionMiddleware, secret_key=settings.SECRET_KEY)
app.include_router(auth_router)

templates = Jinja2Templates(directory="templates")

@app.get("/")
def wasted():
    return {"message": "Wasted time"}

@app.get("/login")
async def login(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/dashboard")
async def dashboard(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    return templates.TemplateResponse("dashboard.html", {"request": request, "user": user})

@app.get("/home")
async def home(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    return templates.TemplateResponse("home.html", {"request": request, "user": user})