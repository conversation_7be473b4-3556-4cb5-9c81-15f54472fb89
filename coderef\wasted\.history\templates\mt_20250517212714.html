<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        select, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #007bff;
        }

        #placeholdersContainer {
            margin-top: 20px;
        }

        .placeholder-input {
            margin-bottom: 15px;
        }

        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }

        #result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        #result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            text-align: center;
            margin: 20px 0;
            color: #666;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Template Management</h1>
        
        <div class="form-group">
            <label for="templateSelect">Select Template:</label>
            <select id="templateSelect">
                <option value="">Select a template...</option>
            </select>
        </div>

        <div id="placeholdersContainer"></div>
        
        <button id="generateBtn" class="btn" style="display: none;">Generate Document</button>
        
        <div id="loading" class="loading">Processing...</div>
        <div id="result"></div>
    </div>

    <script>
        const templateSelect = document.getElementById('templateSelect');
        const placeholdersContainer = document.getElementById('placeholdersContainer');
        const generateBtn = document.getElementById('generateBtn');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');

        // Load templates when page loads
        window.addEventListener('load', loadTemplates);

        async function loadTemplates() {
            try {
                const response = await fetch('/anonymize/list-files');
                const files = await response.json();
                
                templateSelect.innerHTML = '<option value="">Select a template...</option>';
                files.forEach(file => {
                    const option = document.createElement('option');
                    option.value = file;
                    option.textContent = file;
                    templateSelect.appendChild(option);
                });
            } catch (error) {
                showResult('error', `Error loading templates: ${error.message}`);
            }
        }

        templateSelect.addEventListener('change', async function() {
            const filename = this.value;
            if (!filename) {
                placeholdersContainer.innerHTML = '';
                generateBtn.style.display = 'none';
                return;
            }

            try {
                loading.style.display = 'block';
                const response = await fetch(`/anonymize/get-placeholders/${filename}`);
                const placeholders = await response.json();

                placeholdersContainer.innerHTML = '';
                placeholders.forEach(placeholder => {
                    const div = document.createElement('div');
                    div.className = 'placeholder-input';
                    
                    const label = document.createElement('label');
                    label.textContent = placeholder;
                    
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.placeholder = `Enter value for ${placeholder}`;
                    input.dataset.placeholder = placeholder;
                    
                    div.appendChild(label);
                    div.appendChild(input);
                    placeholdersContainer.appendChild(div);
                });

                generateBtn.style.display = 'block';
            } catch (error) {
                showResult('error', `Error loading placeholders: ${error.message}`);
            } finally {
                loading.style.display = 'none';
            }
        });

        generateBtn.addEventListener('click', async function() {
            const filename = templateSelect.value;
            if (!filename) return;

            const inputs = placeholdersContainer.querySelectorAll('input');
            const placeholderValues = {};
            
            inputs.forEach(input => {
                if (input.value.trim()) {
                    placeholderValues[input.dataset.placeholder] = input.value.trim();
                }
            });

            if (Object.keys(placeholderValues).length === 0) {
                showResult('error', 'Please fill in at least one placeholder value');
                return;
            }

            try {
                loading.style.display = 'block';
                const response = await fetch(`/anonymize/generate/${filename}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        placeholder_values: placeholderValues
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `generated_${filename}`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showResult('success', 'Document generated successfully!');
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || 'Failed to generate document');
                }
            } catch (error) {
                showResult('error', `Error generating document: ${error.message}`);
            } finally {
                loading.style.display = 'none';
            }
        });

        function showResult(type, message) {
            result.textContent = message;
            result.className = type;
            result.style.display = 'block';
            
            setTimeout(() => {
                result.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html> 