import pytest
from app.models import User, Repository, Folder, File

def test_user_creation(db_session):
    """Test user model creation."""
    user = User(
        github_id=12345,
        username="testuser",
        email="<EMAIL>",
        name="Test User"
    )
    db_session.add(user)
    db_session.commit()
    
    assert user.id is not None
    assert user.github_id == 12345
    assert user.username == "testuser"
    assert user.display_name == "Test User"
    assert not user.has_repository()

def test_repository_creation(db_session, test_user):
    """Test repository model creation."""
    repository = Repository(
        name="test-repo",
        description="Test repository",
        user_id=test_user.id
    )
    db_session.add(repository)
    db_session.commit()
    
    assert repository.id is not None
    assert repository.name == "test-repo"
    assert repository.user_id == test_user.id
    assert not repository.is_github_synced()

def test_folder_creation(db_session, test_repository):
    """Test folder model creation."""
    folder = Folder(
        name="documents",
        parent_id=test_repository.root_folder.id,
        repository_id=test_repository.id
    )
    db_session.add(folder)
    db_session.commit()
    
    assert folder.id is not None
    assert folder.name == "documents"
    assert folder.get_full_path() == "/documents/"

def test_file_creation(db_session, test_repository):
    """Test file model creation."""
    file = File(
        name="test.txt",
        folder_id=test_repository.root_folder.id,
        size=1024,
        mime_type="text/plain",
        storage_path="test_storage_path.txt"
    )
    db_session.add(file)
    db_session.commit()
    
    assert file.id is not None
    assert file.name == "test.txt"
    assert file.size == 1024
    assert file.extension == "txt"
    assert file.get_full_path() == "/test.txt"

def test_user_repository_relationship(db_session, test_user, test_repository):
    """Test user-repository relationship."""
    db_session.refresh(test_user)
    assert test_user.has_repository()
    assert test_user.repository.id == test_repository.id

def test_folder_hierarchy(db_session, test_repository):
    """Test folder hierarchy."""
    root = test_repository.root_folder
    
    # Create nested folders
    docs = Folder(name="documents", parent_id=root.id, repository_id=test_repository.id)
    db_session.add(docs)
    db_session.flush()
    
    projects = Folder(name="projects", parent_id=docs.id, repository_id=test_repository.id)
    db_session.add(projects)
    db_session.commit()
    
    assert docs.get_full_path() == "/documents/"
    assert projects.get_full_path() == "/documents/projects/"
