import pytest
from app.services.repository_service import RepositoryService
from app.services.filesystem_service import FileSystemService
from app.models import User, Repository

def test_repository_service_create(db_session, test_user):
    """Test repository creation service."""
    repository = RepositoryService.create_repository(
        db_session, test_user, "test-repo", "Test description"
    )
    
    assert repository.name == "test-repo"
    assert repository.description == "Test description"
    assert repository.user_id == test_user.id
    assert repository.root_folder is not None

def test_repository_service_one_per_user(db_session, test_user, test_repository):
    """Test one repository per user constraint."""
    with pytest.raises(ValueError, match="User already has a repository"):
        RepositoryService.create_repository(
            db_session, test_user, "another-repo", "Another description"
        )

def test_filesystem_service_create_folder(db_session, test_repository):
    """Test folder creation service."""
    fs_service = FileSystemService()
    
    folder = fs_service.create_folder(
        db_session, test_repository, "/", "documents"
    )
    
    assert folder.name == "documents"
    assert folder.parent_id == test_repository.root_folder.id

def test_filesystem_service_upload_file(db_session, test_repository):
    """Test file upload service."""
    fs_service = FileSystemService()
    
    file_content = b"Hello, World!"
    file = fs_service.upload_file(
        db_session, test_repository, "/", "hello.txt", file_content
    )
    
    assert file.name == "hello.txt"
    assert file.size == len(file_content)
    assert file.folder_id == test_repository.root_folder.id

def test_filesystem_service_get_contents(db_session, test_repository):
    """Test directory contents service."""
    fs_service = FileSystemService()
    
    # Create some content
    fs_service.create_folder(db_session, test_repository, "/", "documents")
    fs_service.upload_file(db_session, test_repository, "/", "readme.txt", b"README")
    
    contents = fs_service.get_directory_contents(db_session, test_repository, "/")
    
    assert len(contents) == 2
    folder_item = next(item for item in contents if item["type"] == "folder")
    file_item = next(item for item in contents if item["type"] == "file")
    
    assert folder_item["name"] == "documents"
    assert file_item["name"] == "readme.txt"

def test_filesystem_service_normalize_path():
    """Test path normalization."""
    fs_service = FileSystemService()
    
    assert fs_service.normalize_path("") == "/"
    assert fs_service.normalize_path("/") == "/"
    assert fs_service.normalize_path("documents") == "/documents"
    assert fs_service.normalize_path("/documents/") == "/documents"
    assert fs_service.normalize_path("//documents//projects//") == "/documents/projects"
