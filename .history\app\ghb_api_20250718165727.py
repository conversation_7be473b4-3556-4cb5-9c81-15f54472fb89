from fastapi import APIRouter
from fastapi.params import Depends
from fastapi.requests import Request
from fastapi.responses import RedirectResponse

from app import oauth
from app.config import settings
from app.database import get_db
from sqlalchemy.orm import Session
from app.oauth import get_or_create_user

router = APIRouter(prefix="/auth", tags=["auth"])

@router.get("/login")
async def login(request: Request):
    redirect_uri = "http://localhost:8000/auth/callback"
    return await oauth.github.authorize_redirect(request, redirect_uri)

@router.get("/callback")
async def callback(request: Request,  db: Session = Depends(get_db)):
    token = await oauth.github.authorize_access_token(request)
    
    resp = oauth.github.get('user', token=token)
    resp.raise_for_status()
    profile = resp.json()

    user = get_or_create_user(db, profile, token.get('access_token'))

    request.session['user_id'] = user.id

    return RedirectResponse(url="/dash", status_code=302)
