import httpx
from sqlalchemy.orm import Session
from authlib.integrations.starlette_client import OAuth
from starlette.config import Config
from app.config import settings
from app.models import User
import logging

logger = logging.getLogger(__name__)

# OAuth configuration
config = Config()
oauth = OAuth(config)

# Register GitHub OAuth provider
oauth.register(
    name='github',
    client_id=settings.GITHUB_CLIENT_ID,
    client_secret=settings.GITHUB_CLIENT_SECRET,
    authorize_url='https://github.com/login/oauth/authorize',
    access_token_url='https://github.com/login/oauth/access_token',
    client_kwargs={
        'scope': 'user:email repo'  # Include repo scope for repository access
    },
    server_metadata=None,
)

class AuthService:
    """Service for handling authentication operations."""
    
    @staticmethod
    async def get_github_user_info(token: dict) -> dict:
        """Fetch user information from GitHub API."""
        headers = {
            'Authorization': f'token {token["access_token"]}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        async with httpx.AsyncClient() as client:
            # Get user info
            user_response = await client.get(
                'https://api.github.com/user',
                headers=headers
            )
            
            if user_response.status_code != 200:
                logger.error(f"Failed to get user info: {user_response.text}")
                user_response.raise_for_status()
            
            user_data = user_response.json()
            
            # Get user email if not public
            if not user_data.get('email'):
                email_response = await client.get(
                    'https://api.github.com/user/emails',
                    headers=headers
                )
                
                if email_response.status_code == 200:
                    emails = email_response.json()
                    primary_email = next(
                        (email['email'] for email in emails if email['primary']), 
                        None
                    )
                    if primary_email:
                        user_data['email'] = primary_email
            
            return user_data
    
    @staticmethod
    def get_or_create_user(db: Session, user_info: dict, access_token: str = None) -> User:
        """Get existing user or create new one."""
        github_id = user_info['id']
        
        # Try to find existing user
        user = db.query(User).filter(User.github_id == github_id).first()
        
        if user:
            # Update existing user
            user.username = user_info['login']
            user.email = user_info.get('email')
            user.name = user_info.get('name')
            user.avatar_url = user_info.get('avatar_url')
            if access_token:
                user.access_token = access_token
            logger.info(f"Updated existing user: {user.username}")
        else:
            # Create new user
            user = User(
                github_id=github_id,
                username=user_info['login'],
                email=user_info.get('email'),
                name=user_info.get('name'),
                avatar_url=user_info.get('avatar_url'),
                access_token=access_token
            )
            db.add(user)
            logger.info(f"Created new user: {user.username}")
        
        db.commit()
        db.refresh(user)
        return user
