from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from starlette.middleware.sessions import SessionMiddleware
from sqlalchemy.orm import Session
import logging

from .config import settings
from .database import create_tables, get_db, User
from .auth import oauth, get_github_user_info, get_or_create_user, get_current_user

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="GitHub OAuth App",
    description="A simple FastAPI application with GitHub OAuth authentication",
    version="1.0.0"
)

# Add session middleware
app.add_middleware(SessionMiddleware, secret_key=settings.SECRET_KEY)

# Setup templates
templates = Jinja2Templates(directory="templates")

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    create_tables()
    logger.info("Database tables created successfully")
    
    if not settings.is_configured:
        logger.warning("GitHub OAuth is not configured. Please set GITHUB_CLIENT_ID and GITHUB_CLIENT_SECRET environment variables.")

@app.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    """Home page - shows login or user dashboard."""
    user = get_current_user(request, db)
    return templates.TemplateResponse("index.html", {"request": request, "user": user})

@app.get("/profile", response_class=HTMLResponse)
async def profile(request: Request, db: Session = Depends(get_db)):
    """User profile page - requires authentication."""
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    
    return templates.TemplateResponse("profile.html", {"request": request, "user": user})

@app.get("/login")
async def login(request: Request):
    """Initiate GitHub OAuth login."""
    if not settings.is_configured:
        return templates.TemplateResponse("error.html", {
            "request": request, 
            "error_message": "GitHub OAuth is not configured. Please contact the administrator."
        })
    
    redirect_uri = "http://localhost:8000/github-code"
    return await oauth.github.authorize_redirect(request, redirect_uri)

@app.get("/github-code")
async def auth_callback(request: Request, db: Session = Depends(get_db)):
    """Handle GitHub OAuth callback."""
    try:
        # Clear any existing user session before OAuth
        if 'user_id' in request.session:
            del request.session['user_id']
        
        # Get the authorization token with proper state handling
        token = await oauth.github.authorize_access_token(request)
        
        # Get user information from GitHub
        user_info = await get_github_user_info(token)
        
        # Create or update user in database
        user = get_or_create_user(db, user_info)
        
        # Store user ID in session
        request.session['user_id'] = user.id
        
        logger.info(f"User {user.username} logged in successfully")
        return RedirectResponse(url="/", status_code=302)
        
    except Exception as e:
        logger.error(f"OAuth callback error: {str(e)}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error_message": "Authentication failed. Please try again."
        })

@app.get("/logout")
async def logout(request: Request):
    """Logout user by clearing session."""
    request.session.clear()
    return RedirectResponse(url="/", status_code=302)

@app.get("/api/user")
async def get_user_api(request: Request, db: Session = Depends(get_db)):
    """API endpoint to get current user information."""
    user = get_current_user(request, db)
    if not user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    return {
        "id": user.id,
        "github_id": user.github_id,
        "username": user.username,
        "name": user.name,
        "email": user.email,
        "avatar_url": user.avatar_url,
        "created_at": user.created_at.isoformat(),
        "updated_at": user.updated_at.isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "oauth_configured": settings.is_configured
    }

@app.get("/favicon.ico")
async def favicon():
    """Favicon endpoint to prevent 404 errors."""
    return {"message": "No favicon configured"}

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    return templates.TemplateResponse("error.html", {
        "request": request,
        "error_message": "Page not found."
    }, status_code=404)

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    logger.error(f"Internal server error: {str(exc)}")
    return templates.TemplateResponse("error.html", {
        "request": request,
        "error_message": "Internal server error. Please try again later."
    }, status_code=500)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True) 