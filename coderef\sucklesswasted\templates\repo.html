<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Explorer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        custom: {
                            'bg': '#1a1c1c',
                            'surface': '#181a1b'
                        },
                        dark: {
                            '50': '#f6f6f7',
                            '100': '#e1e3e6',
                            '200': '#c2c5cb',
                            '300': '#9ba1ab',
                            '400': '#767d8a',
                            '500': '#5c636f',
                            '600': '#3a404a',
                            '700': '#2a2f38',
                            '800': '#1c2028',
                            '900': '#12151c',
                            '950': '#0a0c10',
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Custom file input styling */
        .file-input-custom::-webkit-file-upload-button {
            display: none;
        }
        .file-input-custom::file-selector-button {
            display: none;
        }

        /* Add fade transition */
        .fade-enter {
            opacity: 0;
        }
        .fade-enter-active {
            opacity: 1;
            transition: opacity 150ms ease-in;
        }
        .fade-exit {
            opacity: 1;
        }
        .fade-exit-active {
            opacity: 0;
            transition: opacity 150ms ease-out;
        }

        /* Loading spinner */
        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border-top: 3px solid #3b82f6;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50 dark:bg-custom-bg transition-colors duration-200">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="loading-spinner dark:border-dark-600 dark:border-t-blue-500"></div>
    </div>

    <div class="bg-white dark:bg-custom-surface shadow-sm sticky top-0 z-20 transition-colors duration-200">
        <div class="container mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                        <i class="far fa-code mr-2 text-gray-600 dark:text-gray-400"></i>
                        File Explorer
                        <span class="ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-dark-800 text-gray-600 dark:text-gray-300 font-normal">
                            Public
                        </span>
                    </h1>
                </div>
                <div class="flex items-center space-x-4 text-sm">
                    <button id="darkModeToggle" class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-dark-800 transition-colors duration-200">
                        <i class="far fa-moon dark:hidden text-gray-600"></i>
                        <i class="far fa-sun hidden dark:inline text-gray-300"></i>
                    </button>
                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                        <i class="far fa-eye mr-1"></i>
                        <span>8</span>
                        <span class="ml-1 hidden sm:inline">watching</span>
                    </div>
                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                        <i class="far fa-star mr-1"></i>
                        <span>42</span>
                        <span class="ml-1 hidden sm:inline">stars</span>
                    </div>
                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                        <i class="far fa-code-fork mr-1"></i>
                        <span>13</span>
                        <span class="ml-1 hidden sm:inline">forks</span>
                    </div>
                </div>
            </div>
            <div class="mt-4 text-sm text-gray-600 dark:text-gray-400 italic">
                Last updated on {{ now.strftime('%Y-%m-%d') }}
            </div>
        </div>
    </div>

    <main class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Error message -->
        <div id="errorContainer" class="hidden"></div>
        
        {% if error %}
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="far fa-exclamation-circle text-red-400 dark:text-red-500"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">{{ error }}</h3>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="bg-white dark:bg-custom-surface rounded-lg shadow transition-colors duration-200">
            <div class="p-4 sm:p-6 border-b border-gray-200 dark:border-dark-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <!-- Breadcrumb -->
                <div class="breadcrumb-container flex items-center text-sm">
                    <a href="/virtual/?path=" class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400">
                        <i class="far fa-home"></i>
                    </a>
                    {% for item in breadcrumbs %}
                        <span class="mx-2 text-gray-500 dark:text-gray-400">/</span>
                        <a href="/virtual/?path={{ item.path }}" class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400">
                            {{ item.name }}
                        </a>
                    {% endfor %}
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <button onclick="showCreateFolderModal()" class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-150 flex items-center text-sm font-medium">
                        <i class="far fa-folder-plus mr-2"></i>
                        <span class="hidden sm:inline">New folder</span>
                        <span class="sm:hidden">Folder</span>
                    </button>
                    <button onclick="showUploadFileModal()" class="px-3 py-2 bg-gray-800 dark:bg-dark-700 text-white rounded-md hover:bg-gray-900 dark:hover:bg-dark-600 transition-colors duration-150 flex items-center text-sm font-medium">
                        <i class="far fa-upload mr-2"></i>
                        <span class="hidden sm:inline">Add file</span>
                        <span class="sm:hidden">File</span>
                    </button>
                </div>
            </div>

            <!-- File Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-dark-700">
                    <thead class="bg-gray-50 dark:bg-custom-surface">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden sm:table-cell">
                                Last Modified
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                                Size
                            </th>
                            <th class="relative px-6 py-3 w-10">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-custom-surface divide-y divide-gray-200 dark:divide-dark-700">
                        {% for item in contents %}
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-800 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i class="far {% if item.type == 'folder' %}fa-folder text-blue-500{% else %}fa-file text-gray-500 dark:text-gray-400{% endif %} mr-2"></i>
                                    {% if item.type == 'folder' %}
                                    <a href="virtual/?path={{ item.path }}" class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 hover:underline text-sm font-medium">{{ item.name }}</a>
                                    {% else %}
                                    <span class="text-gray-900 dark:text-gray-100 text-sm font-medium">{{ item.name }}</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden sm:table-cell">
                                {{ item.last_modified.strftime('%Y-%m-%d %H:%M:%S') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
                                {% if item.size is not none %}
                                    {{ '{:.1f}'.format(item.size / 1024) }} KB
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium w-10">
                                <button class="text-gray-400 dark:text-gray-500 hover:text-gray-900 dark:hover:text-gray-300">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                        
                        {% if not contents %}
                        <tr>
                            <td colspan="4" class="px-6 py-10 text-center text-sm text-gray-500 dark:text-gray-400">
                                This folder is empty
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Create Folder Modal -->
    <div id="createFolderModal" class="hidden fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
        <div class="relative mx-auto p-6 border border-gray-200 dark:border-dark-700 w-[28rem] shadow-xl rounded-lg bg-white dark:bg-custom-surface transform transition-all duration-300">
            <div class="mt-2">
                <div class="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-dark-700">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Create New Folder</h3>
                    <button onclick="closeCreateFolderModal()" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors duration-150">
                        <i class="far fa-times text-lg"></i>
                    </button>
                </div>
                <div class="mt-6">
                    <label for="folderName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Folder Name
                    </label>
                    <input 
                        type="text" 
                        id="folderName" 
                        class="mt-1 block w-full px-4 py-3 bg-white dark:bg-dark-800 border border-gray-300 dark:border-dark-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:text-gray-100 dark:placeholder-gray-500 transition-colors duration-150"
                        placeholder="Enter folder name"
                    >
                    <p class="mt-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-dark-800 p-3 rounded-lg">
                        Creating folder at: <span class="font-mono text-xs bg-gray-100 dark:bg-dark-700 px-2 py-1 rounded">{{ current_path or '/' }}</span>
                    </p>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button 
                        onclick="closeCreateFolderModal()" 
                        class="px-4 py-2.5 bg-gray-100 dark:bg-dark-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors duration-150 text-sm font-medium"
                    >
                        Cancel
                    </button>
                    <button 
                        onclick="createFolder()" 
                        class="px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-150 text-sm font-medium"
                    >
                        Create folder
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload File Modal -->
    <div id="uploadFileModal" class="hidden fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-[32rem] shadow-lg rounded-md bg-white dark:bg-custom-surface transform transition-all duration-300">
            <div class="mt-3">
                <div class="flex justify-between items-center pb-3 border-b dark:border-dark-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Upload Files</h3>
                    <button onclick="closeUploadFileModal()" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <i class="far fa-times"></i>
                    </button>
                </div>
                <div class="mt-4">
                    <div id="dropZone" class="border-2 border-dashed border-gray-300 dark:border-dark-600 rounded-lg p-6 text-center transition-colors duration-150">
                        <i class="far fa-cloud-upload-alt text-4xl text-gray-400 dark:text-gray-500"></i>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            <span class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 cursor-pointer" onclick="document.getElementById('fileInput').click()">Click to upload</span>
                            or drag and drop
                        </p>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Any file types, up to 50MB</p>
                        <input type="file" id="fileInput" multiple class="hidden">
                    </div>
                    <div id="fileList" class="mt-4 hidden">
                        <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Selected files (<span id="fileCount">0</span>)</p>
                        <ul id="selectedFiles" class="max-h-40 overflow-y-auto border border-gray-200 dark:border-dark-700 rounded-md divide-y divide-gray-200 dark:divide-dark-700"></ul>
                    </div>
                    <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">
                        Uploading to: <span class="font-mono text-xs bg-gray-100 dark:bg-dark-800 p-1 rounded">{{ current_path or '/' }}</span>
                    </p>
                </div>
                <div class="mt-5 flex justify-end space-x-2">
                    <button onclick="closeUploadFileModal()" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-dark-700 dark:hover:bg-dark-600 text-gray-800 dark:text-gray-200 rounded-md transition-colors duration-150">Cancel</button>
                    <button onclick="uploadFiles()" id="uploadButton" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-150 opacity-50 cursor-not-allowed" disabled>Upload</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true' || 
            (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            html.classList.add('dark');
        }

        darkModeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.setItem('darkMode', html.classList.contains('dark'));
        });

        // Modal functions
        function showCreateFolderModal() {
            document.getElementById('createFolderModal').classList.remove('hidden');
            document.getElementById('folderName').focus();
            document.getElementById('folderName').value = '';
        }

        function closeCreateFolderModal() {
            document.getElementById('createFolderModal').classList.add('hidden');
            document.getElementById('folderName').value = '';
        }

        function showUploadFileModal() {
            document.getElementById('uploadFileModal').classList.remove('hidden');
        }

        function closeUploadFileModal() {
            document.getElementById('uploadFileModal').classList.add('hidden');
            document.getElementById('fileInput').value = '';
            document.getElementById('fileList').classList.add('hidden');
            document.getElementById('selectedFiles').innerHTML = '';
            document.getElementById('uploadButton').disabled = true;
            document.getElementById('uploadButton').classList.add('opacity-50', 'cursor-not-allowed');
            document.getElementById('dropZone').classList.remove('border-blue-500', 'bg-blue-50', 'dark:border-blue-400', 'dark:bg-blue-900/20');
        }

        // File upload handling
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const selectedFiles = document.getElementById('selectedFiles');
        const fileCount = document.getElementById('fileCount');
        const uploadButton = document.getElementById('uploadButton');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('border-blue-500', 'bg-blue-50', 'dark:border-blue-400', 'dark:bg-blue-900/20');
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-blue-500', 'bg-blue-50', 'dark:border-blue-400', 'dark:bg-blue-900/20');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-blue-500', 'bg-blue-50', 'dark:border-blue-400', 'dark:bg-blue-900/20');
            handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                fileList.classList.remove('hidden');
                selectedFiles.innerHTML = '';
                fileCount.textContent = files.length;
                uploadButton.disabled = false;
                uploadButton.classList.remove('opacity-50', 'cursor-not-allowed');

                Array.from(files).forEach((file, index) => {
                    const li = document.createElement('li');
                    li.className = 'px-3 py-2 flex items-center justify-between';
                    li.innerHTML = `
                        <div class="flex items-center">
                            <i class="far fa-file text-gray-500 dark:text-gray-400 mr-2"></i>
                            <span class="text-sm text-gray-700 dark:text-gray-300 truncate max-w-xs">${file.name}</span>
                            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">(${(file.size / 1024).toFixed(1)} KB)</span>
                        </div>
                        <button onclick="removeFile(${index})" class="text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400">
                            <i class="far fa-times"></i>
                        </button>
                    `;
                    selectedFiles.appendChild(li);
                });
            }
        }

        function removeFile(index) {
            const dt = new DataTransfer();
            const files = fileInput.files;
            for (let i = 0; i < files.length; i++) {
                if (i !== index) dt.items.add(files[i]);
            }
            fileInput.files = dt.files;
            handleFiles(fileInput.files);
        }

        async function createFolder() {
            const folderName = document.getElementById('folderName').value.trim();
            if (!folderName) return;

            if (/[<>:"\/\\|?*]/.test(folderName)) {
                alert('Folder name contains invalid characters');
                return;
            }

            const formData = new FormData();
            formData.append('path', new URLSearchParams(window.location.search).get('path') || '');
            formData.append('name', folderName);

            try {
                const response = await fetch('/create-folder', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    window.location.reload();
                } else {
                    alert('Failed to create folder');
                }
            } catch (error) {
                console.error('Error creating folder:', error);
                alert('Error creating folder');
            }
        }

        async function uploadFiles() {
            const files = fileInput.files;
            if (files.length === 0) return;

            const currentPath = new URLSearchParams(window.location.search).get('path') || '';
            uploadButton.disabled = true;
            uploadButton.textContent = 'Uploading...';

            for (const file of files) {
                const formData = new FormData();
                formData.append('path', currentPath);
                formData.append('file', file);

                try {
                    const response = await fetch('/upload-file', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    if (!response.ok) {
                        console.error('Error uploading file:', file.name, result.error);
                        alert(`Failed to upload ${file.name}: ${result.error}`);
                    }
                } catch (error) {
                    console.error('Error uploading file:', error);
                    alert(`Error uploading file: ${file.name}`);
                }
            }

            window.location.reload();
        }

        // Handle Enter key in the folder name input
        document.getElementById('folderName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                createFolder();
            }
        });

        // State management
        let currentState = {
            path: new URLSearchParams(window.location.search).get('path') || '/',
            contents: [],
            breadcrumbs: []
        };

        // Update browser history without reload
        function updateHistory(path) {
            const url = new URL(window.location.href);
            url.searchParams.set('path', path);
            window.history.pushState({ path }, '', url);
        }

        // Show/hide loading overlay
        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        // Fetch directory contents
        async function fetchDirectoryContents(path) {
            showLoading();
            try {
                const response = await fetch(`/api/contents?path=${encodeURIComponent(path)}`);
                if (!response.ok) throw new Error('Failed to fetch directory contents');
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error fetching directory contents:', error);
                throw error;
            } finally {
                hideLoading();
            }
        }

        // Update the view with new content
        function updateView(data) {
            const tableBody = document.querySelector('tbody');
            const breadcrumbContainer = document.querySelector('.breadcrumb-container');
            
            // Handle error if present
            const errorContainer = document.querySelector('#errorContainer');
            if (data.error) {
                errorContainer.innerHTML = `
                    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="far fa-exclamation-circle text-red-400 dark:text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">${data.error}</h3>
                            </div>
                        </div>
                    </div>
                `;
                errorContainer.classList.remove('hidden');
            } else {
                errorContainer.classList.add('hidden');
            }
            
            // Update breadcrumbs
            breadcrumbContainer.innerHTML = `
                <a href="/virtual/?path=" class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400">
                    <i class="far fa-home"></i>
                </a>
                ${data.breadcrumbs.map((item, index) => `
                    <span class="mx-2 text-gray-500 dark:text-gray-400">/</span>
                    <a href="/virtual/?path=${encodeURIComponent(item.path)}" 
                       class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400">
                        ${escapeHtml(item.name)}
                    </a>
                `).join('')}
            `;

            // Update file list
            tableBody.innerHTML = data.contents.length ? data.contents.map(item => `
                <tr class="hover:bg-gray-50 dark:hover:bg-dark-800 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <i class="far ${item.type === 'folder' ? 'fa-folder text-blue-500' : 'fa-file text-gray-500 dark:text-gray-400'} mr-2"></i>
                            ${item.type === 'folder' 
                                ? `<a href="/virtual/?path=${encodeURIComponent(item.path)}" 
                                     class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 hover:underline text-sm font-medium">
                                     ${escapeHtml(item.name)}</a>`
                                : `<span class="text-gray-900 dark:text-gray-100 text-sm font-medium">${escapeHtml(item.name)}</span>`
                            }
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden sm:table-cell">
                        ${new Date(item.last_modified).toLocaleString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
                        ${item.size ? (item.size / 1024).toFixed(1) + ' KB' : '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium w-10">
                        <button class="text-gray-400 dark:text-gray-500 hover:text-gray-900 dark:hover:text-gray-300">
                            <i class="far fa-ellipsis-v"></i>
                        </button>
                    </td>
                </tr>
            `).join('') : `
                <tr>
                    <td colspan="4" class="px-6 py-10 text-center text-sm text-gray-500 dark:text-gray-400">
                        This folder is empty
                    </td>
                </tr>
            `;
        }

        // Helper function to escape HTML
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // Navigation function
        async function navigateTo(path) {
            try {
                const data = await fetchDirectoryContents(path);
                updateHistory(path);
                currentState = {
                    path,
                    ...data
                };
                updateView(data);
            } catch (error) {
                alert('Failed to navigate to directory');
            }
        }

        // Handle browser back/forward
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.path) {
                navigateTo(event.state.path);
            }
        });

        // Add transition styles
        document.querySelector('tbody').style.transition = 'opacity 150ms ease-in-out';
        document.querySelector('.breadcrumb-container').style.transition = 'opacity 150ms ease-in-out';
    </script>
</body>
</html> 