from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, UniqueConstraint
from sqlalchemy.orm import relationship
from .base import BaseModel

class Repository(BaseModel):
    """Repository model for storing user's virtual repository information."""
    
    __tablename__ = "repositories"
    
    name = Column(String(255), nullable=False)
    description = Column(String(1000), nullable=True)
    github_repo_name = Column(String(255), nullable=True)  # Actual GitHub repository name
    github_repo_url = Column(String(500), nullable=True)   # GitHub repository URL
    is_synced = Column(Boolean, default=False)             # Whether it's synced with GitHub
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, unique=True)
    
    # Relationships
    user = relationship("User", back_populates="repository")
    folders = relationship("Folder", back_populates="repository", cascade="all, delete-orphan")
    
    # Ensure one repository per user
    __table_args__ = (UniqueConstraint('user_id', name='_user_repository_uc'),)
    
    def __repr__(self):
        return f"<Repository(id={self.id}, name='{self.name}', user_id={self.user_id})>"
    
    @property
    def full_name(self):
        """Return the full repository name (username/repo_name)."""
        if self.user and self.github_repo_name:
            return f"{self.user.username}/{self.github_repo_name}"
        return self.name
    
    def is_github_synced(self):
        """Check if repository is synced with GitHub."""
        return self.is_synced and self.github_repo_url is not None

    @property
    def root_folder(self):
        """Get the root folder for this repository."""
        return next((folder for folder in self.folders if folder.parent_id is None), None)
