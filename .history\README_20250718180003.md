# Wasted Time

A simple FastAPI application with GitHub OAuth authentication.

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Create a `.env` file with your GitHub OAuth credentials:
```
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here
SECRET_KEY=your_secret_key_here_change_in_production
```

3. Run the application:
```bash
python run.py
```

## GitHub OAuth Setup

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL to: `http://localhost:8000/auth/callback`
4. Copy the Client ID and Client Secret to your `.env` file

## Routes

- `/` - Main page
- `/login` - Login page
- `/auth/login` - GitHub OAuth login
- `/auth/callback` - OAuth callback
- `/auth/logout` - Logout
- `/dashboard` - User dashboard (requires login)
- `/home` - User home page (requires login) 