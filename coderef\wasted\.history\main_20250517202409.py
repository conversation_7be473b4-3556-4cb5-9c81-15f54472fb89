from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Request
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import shutil
from pathlib import Path
from typing import Optional
import os
import google.generativeai as genai
from dotenv import load_dotenv
from PyPDF2 import PdfReader
import filetype
import docx
import odf.opendocument
import odf.text
from openpyxl import load_workbook
import io

# Load environment variables
load_dotenv()

# Configure Gemini API
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
genai.configure(api_key=GOOGLE_API_KEY)

app = FastAPI(title="File Upload API")

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# Create templates directory if it doesn't exist
TEMPLATES_DIR = Path("templates")
TEMPLATES_DIR.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

async def extract_docx_text(file_path: Path) -> str:
    """Extract text from DOCX file"""
    try:
        doc = docx.Document(file_path)
        return "\n".join([paragraph.text for paragraph in doc.paragraphs])
    except Exception as e:
        return f"Error extracting DOCX text: {str(e)}"

async def extract_xlsx_text(file_path: Path) -> str:
    """Extract text from XLSX file"""
    try:
        wb = load_workbook(filename=file_path, read_only=True)
        text = []
        for sheet in wb.sheetnames:
            ws = wb[sheet]
            text.append(f"\nSheet: {sheet}")
            for row in ws.iter_rows(values_only=True):
                text.append("\t".join(str(cell) if cell is not None else "" for cell in row))
        return "\n".join(text)
    except Exception as e:
        return f"Error extracting XLSX text: {str(e)}"

async def extract_odt_text(file_path: Path) -> str:
    """Extract text from ODT file"""
    try:
        doc = odf.opendocument.load(str(file_path))
        text_elements = doc.getElementsByType(odf.text.P)
        return "\n".join([element.firstChild.data if element.firstChild else "" for element in text_elements])
    except Exception as e:
        return f"Error extracting ODT text: {str(e)}"

async def extract_pdf_text(file_path: Path) -> str:
    """Extract text from PDF file"""
    try:
        reader = PdfReader(file_path)
        text = []
        for page in reader.pages:
            text.append(page.extract_text())
        return "\n".join(text)
    except Exception as e:
        return f"Error extracting PDF text: {str(e)}"

async def analyze_with_gemini(content: str, file_type: str, filename: str) -> str:
    """Analyze file contents using Gemini API"""
    try:
        model = genai.GenerativeModel('gemini-pro')
        prompt = f"""Analyze this {file_type} file content from {filename} and explain what it contains. 
        If it's code, explain what the code does. If it's a document, summarize the main points.
        If it's a spreadsheet, describe the data structure and key information.
        Be detailed but concise in your analysis."""
        
        response = model.generate_content(prompt + "\n\nContent:\n" + content)
        return response.text
    except Exception as e:
        return f"Error analyzing content with Gemini: {str(e)}"

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Render the upload form"""
    return templates.TemplateResponse("upload.html", {"request": request})

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a file and return its contents"""
    try:
        contents = await file.read()
        
        # Save the file
        file_path = UPLOAD_DIR / file.filename
        with open(file_path, "wb") as f:
            f.write(contents)
        
        # Try to detect file type
        kind = filetype.guess(file_path)
        mime_type = kind.mime if kind else "application/octet-stream"
        
        # Try to read as text if not a binary file
        try:
            text_contents = contents.decode('utf-8')
            return {
                "filename": file.filename,
                "content_type": mime_type,
                "contents": text_contents
            }
        except UnicodeDecodeError:
            return {
                "filename": file.filename,
                "content_type": mime_type,
                "size_bytes": len(contents),
                "message": "File contents are binary and cannot be displayed as text"
            }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while processing the file: {str(e)}"}
        )

@app.post("/analyze")
async def analyze_file(file: UploadFile = File(...)):
    """Upload a file and analyze its contents using Gemini"""
    try:
        contents = await file.read()
        file_path = UPLOAD_DIR / file.filename
        
        # Save the file temporarily
        with open(file_path, "wb") as f:
            f.write(contents)

        # Get file extension
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
        
        # Extract text based on file type
        text_content = ""
        file_type = "document"
        
        if file_ext == 'pdf':
            text_content = await extract_pdf_text(file_path)
            file_type = "PDF document"
        elif file_ext == 'docx':
            text_content = await extract_docx_text(file_path)
            file_type = "Word document"
        elif file_ext == 'xlsx':
            text_content = await extract_xlsx_text(file_path)
            file_type = "Excel spreadsheet"
        elif file_ext == 'odt':
            text_content = await extract_odt_text(file_path)
            file_type = "OpenDocument text"
        else:
            try:
                text_content = contents.decode('utf-8')
                file_type = "text file"
            except UnicodeDecodeError:
                return JSONResponse(
                    status_code=400,
                    content={"message": "File type not supported for analysis"}
                )
        
        # Analyze with Gemini
        analysis = await analyze_with_gemini(text_content, file_type, file.filename)
        
        return {
            "filename": file.filename,
            "file_type": file_type,
            "analysis": analysis
        }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while analyzing the file: {str(e)}"}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 