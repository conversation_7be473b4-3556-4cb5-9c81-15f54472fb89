from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from app.dependencies import require_auth, get_user_repository
from app.models import User, Repository
from app.services.filesystem_service import FileSystemService
from typing import List, Dict, Any

router = APIRouter()
fs_service = FileSystemService()

@router.get("/user")
async def get_current_user_api(user: User = Depends(require_auth)):
    """Get current user information."""
    return {
        "id": user.id,
        "github_id": user.github_id,
        "username": user.username,
        "name": user.name,
        "email": user.email,
        "avatar_url": user.avatar_url,
        "created_at": user.created_at.isoformat(),
        "updated_at": user.updated_at.isoformat(),
        "has_repository": user.has_repository()
    }

@router.get("/repository")
async def get_repository_api(
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository)
):
    """Get repository information."""
    return {
        "id": repository.id,
        "name": repository.name,
        "description": repository.description,
        "github_repo_name": repository.github_repo_name,
        "github_repo_url": repository.github_repo_url,
        "is_synced": repository.is_synced,
        "created_at": repository.created_at.isoformat(),
        "updated_at": repository.updated_at.isoformat()
    }

@router.get("/filesystem")
async def get_filesystem_contents(
    path: str = "/",
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """Get filesystem contents for a given path."""
    try:
        contents = fs_service.get_directory_contents(db, repository, path)
        return contents
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get directory contents: {str(e)}")

@router.get("/health")
async def health_check():
    """API health check."""
    return {"status": "healthy", "service": "api"}
