<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
        }

        h1 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .upload-area.dragover {
            border-color: #28a745;
            background-color: #e8f5e9;
        }

        #fileInput {
            display: none;
        }

        .upload-icon {
            font-size: 3rem;
            color: #666;
            margin-bottom: 1rem;
        }

        .upload-text {
            color: #666;
            margin-bottom: 1rem;
        }

        .btn {
            background-color: #007bff;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        #result {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 5px;
            display: none;
        }

        #result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        #result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .file-info {
            margin-top: 1rem;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>File Upload</h1>
        <div class="upload-area" id="dropZone" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📁</div>
            <p class="upload-text">Drag & Drop files here or click to select</p>
            <input type="file" id="fileInput" />
        </div>
        <div id="result"></div>
    </div>

    <script>
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const result = document.getElementById('result');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);
        fileInput.addEventListener('change', handleFiles, false);

        function preventDefaults (e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            dropZone.classList.add('dragover');
        }

        function unhighlight(e) {
            dropZone.classList.remove('dragover');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles({ target: { files: files } });
        }

        function handleFiles(e) {
            const files = e.target.files;
            uploadFile(files[0]);
        }

        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('success', `
                        <strong>File uploaded successfully!</strong>
                        <div class="file-info">
                            <p>Filename: ${data.filename}</p>
                            <p>Content Type: ${data.content_type}</p>
                            ${data.contents ? `<p>Contents: ${data.contents}</p>` : 
                             `<p>Size: ${data.size_bytes} bytes</p>`}
                        </div>
                    `);
                } else {
                    throw new Error(data.message || 'Upload failed');
                }
            } catch (error) {
                showResult('error', `<strong>Error:</strong> ${error.message}`);
            }
        }

        function showResult(type, message) {
            result.innerHTML = message;
            result.className = type;
            result.style.display = 'block';
        }
    </script>
</body>
</html> 