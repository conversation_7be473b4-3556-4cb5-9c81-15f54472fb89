from fastapi import APIRouter
from fastapi.requests import Request

from app import oauth
from app.config import settings

router = APIRouter(prefix="/auth", tags=["auth"])

@router.get("/login")
async def login(request: Request):
    redirect_uri = "http://localhost:8000/auth/callback"
    return await oauth.github.authorize_redirect(request, redirect_uri)

@router.get("/callback")
async def callback(request: Request):
    token = await oauth.github.authorize_access_token(request)
    github_user = oauth.github.get()
    user_info = await oauth.github.get(settings.GITHUB_USER_URL, token=token)
    return {"message": "Callback"}

