import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.database import get_db
from app.models.base import Base
from app.models import User, Repository, Folder, File

# Test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture
def db_session():
    """Create a fresh database session for each test."""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()

@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    user = User(
        github_id=12345,
        username="testuser",
        email="<EMAIL>",
        name="Test User",
        avatar_url="https://example.com/avatar.jpg",
        access_token="test_token"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def test_repository(db_session, test_user):
    """Create a test repository with root folder."""
    repository = Repository(
        name="test-repo",
        description="Test repository",
        user_id=test_user.id
    )
    db_session.add(repository)
    db_session.flush()
    
    # Create root folder
    root_folder = Folder(
        name="root",
        parent_id=None,
        repository_id=repository.id
    )
    db_session.add(root_folder)
    db_session.commit()
    db_session.refresh(repository)
    return repository
