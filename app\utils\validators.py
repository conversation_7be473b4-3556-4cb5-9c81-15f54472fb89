import re
from typing import List

def validate_repository_name(name: str) -> List[str]:
    """Validate repository name and return list of errors."""
    errors = []
    
    if not name or len(name.strip()) == 0:
        errors.append("Repository name cannot be empty")
        return errors
    
    name = name.strip()
    
    # GitHub repository name rules
    if len(name) > 100:
        errors.append("Repository name cannot exceed 100 characters")
    
    if not re.match(r'^[a-zA-Z0-9._-]+$', name):
        errors.append("Repository name can only contain letters, numbers, dots, hyphens, and underscores")
    
    if name.startswith('.') or name.endswith('.'):
        errors.append("Repository name cannot start or end with a dot")
    
    if name.startswith('-') or name.endswith('-'):
        errors.append("Repository name cannot start or end with a hyphen")
    
    # Reserved names
    reserved_names = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4', 'com5', 
                     'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2', 'lpt3', 'lpt4', 'lpt5', 
                     'lpt6', 'lpt7', 'lpt8', 'lpt9']
    
    if name.lower() in reserved_names:
        errors.append("Repository name cannot be a reserved system name")
    
    return errors

def validate_folder_name(name: str) -> List[str]:
    """Validate folder name and return list of errors."""
    errors = []
    
    if not name or len(name.strip()) == 0:
        errors.append("Folder name cannot be empty")
        return errors
    
    name = name.strip()
    
    if len(name) > 255:
        errors.append("Folder name cannot exceed 255 characters")
    
    # Invalid characters for file systems
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
    for char in invalid_chars:
        if char in name:
            errors.append(f"Folder name cannot contain '{char}'")
    
    if name in ['.', '..']:
        errors.append("Folder name cannot be '.' or '..'")
    
    if name.startswith(' ') or name.endswith(' '):
        errors.append("Folder name cannot start or end with spaces")
    
    return errors

def validate_file_name(name: str) -> List[str]:
    """Validate file name and return list of errors."""
    errors = []
    
    if not name or len(name.strip()) == 0:
        errors.append("File name cannot be empty")
        return errors
    
    name = name.strip()
    
    if len(name) > 255:
        errors.append("File name cannot exceed 255 characters")
    
    # Invalid characters for file systems
    invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
    for char in invalid_chars:
        if char in name:
            errors.append(f"File name cannot contain '{char}'")
    
    if name in ['.', '..']:
        errors.append("File name cannot be '.' or '..'")
    
    if name.startswith(' ') or name.endswith(' '):
        errors.append("File name cannot start or end with spaces")
    
    return errors
