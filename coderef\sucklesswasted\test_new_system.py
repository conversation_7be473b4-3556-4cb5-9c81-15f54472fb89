#!/usr/bin/env python3
"""
Test script for the new SQLAlchemy-based file system
"""

from ghb.models import Folder, File, SessionLocal, init_database
from pathlib import Path
import tempfile
import os

def test_folder_operations():
    """Test folder creation and path operations"""
    print("Testing folder operations...")
    
    # Initialize database
    init_database()
    
    # Create a session
    db = SessionLocal()
    
    try:
        # Test root folder creation
        root = Folder.get_or_create_path(db, "/")
        print(f"Root folder: {root}")
        print(f"Root path: {root.get_full_path()}")
        
        # Test nested folder creation
        nested_folder = Folder.get_or_create_path(db, "/documents/projects/web")
        print(f"Nested folder: {nested_folder}")
        print(f"Nested path: {nested_folder.get_full_path()}")
        
        # Test another path
        another_folder = Folder.get_or_create_path(db, "/images/photos")
        print(f"Another folder: {another_folder}")
        print(f"Another path: {another_folder.get_full_path()}")
        
        # List all folders
        all_folders = db.query(Folder).all()
        print(f"\nAll folders ({len(all_folders)}):")
        for folder in all_folders:
            print(f"  ID: {folder.id}, Name: '{folder.name}', Parent: {folder.parent_id}, Path: {folder.get_full_path()}")
        
        # Test file creation
        print(f"\nTesting file creation...")
        
        # Create a test file in the nested folder
        test_file = File(
            name="test.txt",
            folder_id=nested_folder.id,
            size=1024,
            mime_type="text/plain",
            storage_path="test_storage_path.txt",
            path=f"{nested_folder.get_full_path()}test.txt"
        )
        db.add(test_file)
        db.commit()
        
        print(f"Created file: {test_file}")
        print(f"File path: {test_file.get_full_path()}")
        
        # List all files
        all_files = db.query(File).all()
        print(f"\nAll files ({len(all_files)}):")
        for file in all_files:
            print(f"  ID: {file.id}, Name: '{file.name}', Folder: {file.folder_id}, Path: {file.get_full_path()}")
        
        print("\n✅ All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        db.rollback()
    finally:
        db.close()

def test_path_traversal():
    """Test path traversal and folder lookup"""
    print("\nTesting path traversal...")
    
    db = SessionLocal()
    
    try:
        from ghb.repo import get_folder_by_path, get_virtual_directory_contents
        
        # Test getting folder by path
        root_folder = get_folder_by_path(db, "/")
        print(f"Root folder lookup: {root_folder}")
        
        docs_folder = get_folder_by_path(db, "/documents")
        print(f"Documents folder lookup: {docs_folder}")
        
        web_folder = get_folder_by_path(db, "/documents/projects/web")
        print(f"Web folder lookup: {web_folder}")
        
        # Test directory contents
        root_contents = get_virtual_directory_contents(db, "/")
        print(f"\nRoot directory contents ({len(root_contents)} items):")
        for item in root_contents:
            print(f"  {item['type']}: {item['name']} -> {item['path']}")
        
        if docs_folder:
            docs_contents = get_virtual_directory_contents(db, "/documents")
            print(f"\nDocuments directory contents ({len(docs_contents)} items):")
            for item in docs_contents:
                print(f"  {item['type']}: {item['name']} -> {item['path']}")
        
        if web_folder:
            web_contents = get_virtual_directory_contents(db, "/documents/projects/web")
            print(f"\nWeb directory contents ({len(web_contents)} items):")
            for item in web_contents:
                print(f"  {item['type']}: {item['name']} -> {item['path']}")
        
        print("\n✅ Path traversal tests passed!")
        
    except Exception as e:
        print(f"❌ Path traversal test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Testing new SQLAlchemy-based file system")
    print("=" * 50)
    
    test_folder_operations()
    test_path_traversal()
    
    print("\n" + "=" * 50)
    print("🎉 Testing complete!") 