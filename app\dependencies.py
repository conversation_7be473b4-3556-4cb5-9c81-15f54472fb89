from fastapi import Request, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from app.models import User
from typing import Optional

def get_current_user(request: Request, db: Session = Depends(get_db)) -> Optional[User]:
    """Get the current authenticated user from session."""
    user_id = request.session.get('user_id')
    if not user_id:
        return None
    
    user = db.query(User).filter(User.id == user_id).first()
    return user

def require_auth(request: Request, db: Session = Depends(get_db)) -> User:
    """Require authentication - raise HTTPException if not authenticated."""
    user = get_current_user(request, db)
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")
    return user

def get_user_repository(user: User = Depends(require_auth)):
    """Get the user's repository - raise HTTPException if not found."""
    if not user.repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    return user.repository
