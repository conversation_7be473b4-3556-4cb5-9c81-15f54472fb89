from fastapi import <PERSON><PERSON><PERSON>
from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.requests import Request

from app.oauth import oauth
from app.config import settings

app = FastAPI()
templates = Jinja2Templates(directory="templates")


@app.get("/")
def wasted():
    return {"message":  "Wasted time"}

@app.get("/login")
async def login(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/home")
async def home(request: Request):
    token = await oauth.github.authorize_access_token(request)
    user_info = await oauth.github.get(settings.GITHUB_USER_URL, token=token)
    return templates.TemplateResponse("home.html", {"request": request, "user_info": user_info})