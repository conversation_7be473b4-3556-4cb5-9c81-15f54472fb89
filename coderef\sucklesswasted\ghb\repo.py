from fastapi import APIRouter, Request, UploadFile, Form, Depends, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pathlib import Path
import os
import shutil
import uuid
from datetime import datetime
import mimetypes
from sqlalchemy.orm import Session

from .models import Folder, File, get_db, init_database

router = APIRouter()

# Get the absolute path to the templates directory
BASE_DIR = Path(__file__).resolve().parent.parent
TEMPLATES_DIR = BASE_DIR / "templates"
STORAGE_DIR = BASE_DIR / "storage"

# Mount static files
router.mount("/static", StaticFiles(directory=str(BASE_DIR / "static")), name="static")

# Setup templates
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))

# Create storage directory if it doesn't exist
STORAGE_DIR.mkdir(exist_ok=True)

# Initialize database on startup
init_database()

def normalize_path(path: str) -> str:
    """Normalize virtual path to ensure consistent format"""
    if not path or path == "/":
        return "/"
    path = path.strip("/")
    return f"/{path}/"

def get_path_segments(path: str):
    """Get path segments for breadcrumb navigation"""
    if path == "/" or not path:
        return []
    
    segments = []
    current = ""
    parts = path.strip("/").split("/")
    
    for part in parts:
        if part:
            current = f"{current}/{part}"
            segments.append({"name": part, "path": current})
    
    return segments

def get_folder_by_path(db: Session, path: str) -> Folder:
    """Get folder by virtual path"""
    if path == "/" or path == "":
        # Return root folder
        return db.query(Folder).filter_by(parent_id=None, name="root").first()
    
    # Split path and traverse
    path = path.strip("/")
    parts = path.split("/")
    
    current_folder = db.query(Folder).filter_by(parent_id=None, name="root").first()
    if not current_folder:
        return None
    
    for part in parts:
        if not part:
            continue
        current_folder = db.query(Folder).filter_by(
            parent_id=current_folder.id, 
            name=part
        ).first()
        if not current_folder:
            return None
    
    return current_folder

def get_virtual_directory_contents(db: Session, path: str):
    """Get contents of a virtual directory"""
    folder = get_folder_by_path(db, path)
    if not folder:
        return []
    
    items = []
    
    # Add subfolders
    subfolders = db.query(Folder).filter_by(parent_id=folder.id).all()
    for subfolder in subfolders:
        items.append({
            "id": subfolder.id,
            "name": subfolder.name,
            "type": "folder",
            "size": None,
            "last_modified": subfolder.updated_at,
            "path": subfolder.get_full_path().rstrip('/')
        })
    
    # Add files
    files = db.query(File).filter_by(folder_id=folder.id).all()
    for file in files:
        items.append({
            "id": file.id,
            "name": file.name,
            "type": "file",
            "size": file.size,
            "last_modified": file.updated_at,
            "path": file.get_full_path()
        })
    
    # Sort: folders first, then files, both alphabetically
    return sorted(items, key=lambda x: (x["type"] != "folder", x["name"].lower()))

@router.get("/virtual", response_class=HTMLResponse)
async def virtual_root(request: Request, path: str = "", db: Session = Depends(get_db)):
    """Main file explorer page"""
    path = normalize_path(path)
    
    # Check if path exists
    folder = get_folder_by_path(db, path)
    if not folder and path != "/":
        return templates.TemplateResponse(
            "repo.html",
            {
                "request": request,
                "error": "Directory not found",
                "current_path": path,
                "contents": [],
                "breadcrumbs": get_path_segments(path),
                "now": datetime.now()
            },
            status_code=404
        )
    
    contents = get_virtual_directory_contents(db, path)
    breadcrumbs = get_path_segments(path)
    
    return templates.TemplateResponse(
        "repo.html",
        {
            "request": request,
            "current_path": path,
            "contents": contents,
            "breadcrumbs": breadcrumbs,
            "now": datetime.now()
        }
    )

@router.post("/create-folder")
async def create_folder(
    path: str = Form(...), 
    name: str = Form(...), 
    db: Session = Depends(get_db)
):
    """Create a new folder"""
    try:
        # Validate folder name
        name = name.strip()
        if not name or "/" in name or "\\" in name:
            return JSONResponse(
                content={"error": "Invalid folder name"}, 
                status_code=400
            )
        
        # Normalize path
        path = normalize_path(path)
        
        # Get parent folder
        parent_folder = get_folder_by_path(db, path)
        if not parent_folder:
            return JSONResponse(
                content={"error": "Parent directory does not exist"}, 
                status_code=400
            )
        
        # Check if folder already exists
        existing = db.query(Folder).filter_by(
            parent_id=parent_folder.id, 
            name=name
        ).first()
        if existing:
            return JSONResponse(
                content={"error": "Folder already exists"}, 
                status_code=400
            )
        
        # Create new folder
        new_folder = Folder(name=name, parent_id=parent_folder.id)
        db.add(new_folder)
        db.commit()
        
        return JSONResponse(content={"success": True})
        
    except Exception as e:
        db.rollback()
        return JSONResponse(
            content={"error": f"Failed to create folder: {str(e)}"}, 
            status_code=500
        )

@router.post("/upload-file")
async def upload_file(
    file: UploadFile, 
    path: str = Form(...), 
    db: Session = Depends(get_db)
):
    """Upload a file to the specified directory"""
    try:
        # Normalize path
        path = normalize_path(path)
        
        # Get target folder
        target_folder = get_folder_by_path(db, path)
        if not target_folder:
            return JSONResponse(
                content={"error": "Directory does not exist"}, 
                status_code=400
            )
        
        # Check if file already exists
        existing_file = db.query(File).filter_by(
            folder_id=target_folder.id,
            name=file.filename
        ).first()
        if existing_file:
            return JSONResponse(
                content={"error": "A file with this name already exists in this location"}, 
                status_code=400
            )
        
        # Generate unique storage filename
        file_id = str(uuid.uuid4())
        file_ext = os.path.splitext(file.filename)[1]
        storage_filename = f"{file_id}{file_ext}"
        storage_path = STORAGE_DIR / storage_filename
        
        # Save file to storage
        with storage_path.open("wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Get file info
        file_size = storage_path.stat().st_size
        mime_type, _ = mimetypes.guess_type(file.filename)
        
        # Create file record
        new_file = File(
            name=file.filename,
            folder_id=target_folder.id,
            size=file_size,
            mime_type=mime_type,
            storage_path=storage_filename,
            path=f"{target_folder.get_full_path().rstrip('/')}/{file.filename}"
        )
        
        db.add(new_file)
        db.commit()
        
        return JSONResponse(content={"success": True})
        
    except Exception as e:
        db.rollback()
        # Clean up uploaded file if database operation failed
        if 'storage_path' in locals() and storage_path.exists():
            storage_path.unlink()
        return JSONResponse(
            content={"error": f"Failed to upload file: {str(e)}"}, 
            status_code=500
        )

@router.get("/api/contents")
async def api_get_contents(path: str = "", db: Session = Depends(get_db)):
    """API endpoint to get directory contents (for AJAX requests)"""
    try:
        path = normalize_path(path)
        
        # Check if path exists
        folder = get_folder_by_path(db, path)
        if not folder and path != "/":
            return JSONResponse(
                content={"error": "Directory not found"}, 
                status_code=404
            )
        
        contents = get_virtual_directory_contents(db, path)
        breadcrumbs = get_path_segments(path)
        
        # Convert datetime objects to strings for JSON serialization
        for item in contents:
            if isinstance(item["last_modified"], datetime):
                item["last_modified"] = item["last_modified"].isoformat()
        
        return JSONResponse(content={
            "contents": contents,
            "breadcrumbs": breadcrumbs,
            "current_path": path
        })
        
    except Exception as e:
        return JSONResponse(
            content={"error": f"Failed to fetch directory contents: {str(e)}"}, 
            status_code=500
        )

@router.get("/api/debug/db-contents")
async def debug_db_contents(db: Session = Depends(get_db)):
    """Debug endpoint to check database contents"""
    try:
        # Get all folders
        folders = db.query(Folder).all()
        folder_data = []
        for folder in folders:
            folder_data.append({
                "id": folder.id,
                "name": folder.name,
                "parent_id": folder.parent_id,
                "full_path": folder.get_full_path(),
                "created_at": folder.created_at.isoformat() if folder.created_at else None
            })
        
        # Get all files
        files = db.query(File).all()
        file_data = []
        for file in files:
            file_data.append({
                "id": file.id,
                "name": file.name,
                "folder_id": file.folder_id,
                "full_path": file.get_full_path(),
                "size": file.size,
                "mime_type": file.mime_type,
                "created_at": file.created_at.isoformat() if file.created_at else None
            })
        
        return JSONResponse(content={
            "total_folders": len(folders),
            "total_files": len(files),
            "folders": folder_data,
            "files": file_data
        })
        
    except Exception as e:
        return JSONResponse(
            content={"error": f"Failed to fetch debug info: {str(e)}"}, 
            status_code=500
        )
