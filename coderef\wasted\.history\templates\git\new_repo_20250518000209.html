{% extends "git/base.html" %}

{% block title %}Create New Repository{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">Create New Repository</h1>
    
    <div class="bg-white rounded-lg shadow-md p-6">
        <form id="newRepoForm" class="space-y-6">
            <div>
                <label for="name" class="block text-gray-700 font-medium mb-2">Repository Name</label>
                <input type="text" id="name" name="name" 
                    class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    pattern="[a-zA-Z0-9_-]+"
                    title="Only letters, numbers, underscores, and hyphens are allowed">
                <p class="text-sm text-gray-500 mt-1">Only letters, numbers, underscores, and hyphens are allowed.</p>
            </div>
            
            <div>
                <label for="owner" class="block text-gray-700 font-medium mb-2">Owner Name</label>
                <input type="text" id="owner" name="owner" 
                    class="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    pattern="[a-zA-Z0-9_-]+"
                    title="Only letters, numbers, underscores, and hyphens are allowed">
                <p class="text-sm text-gray-500 mt-1">This will be used to organize repositories by owner.</p>
            </div>
            
            <div class="flex justify-end space-x-3">
                <a href="/git" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Create Repository
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('newRepoForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    try {
        const response = await fetch('/git/new', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.href = '/git';
        } else {
            alert('Error creating repository: ' + data.message);
        }
    } catch (error) {
        alert('Error creating repository: ' + error.message);
    }
});
</script>
{% endblock %} 