<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Anonymizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
        }

        h1 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .upload-area.dragover {
            border-color: #28a745;
            background-color: #e8f5e9;
        }

        #fileInput {
            display: none;
        }

        .upload-icon {
            font-size: 3rem;
            color: #666;
            margin-bottom: 1rem;
        }

        .upload-text {
            color: #666;
            margin-bottom: 1rem;
        }

        .fields-container {
            margin: 1rem 0;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .fields-title {
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }

        .fields-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .field-tag {
            background-color: #e9ecef;
            padding: 0.3rem 0.6rem;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #495057;
        }

        .custom-field {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin-top: 0.5rem;
        }

        .btn {
            background-color: #007bff;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        #result {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 5px;
            display: none;
        }

        #result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        #result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 1rem;
        }

        .loading::after {
            content: "...";
            animation: dots 1.5s steps(5, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60%, 100% { content: "..."; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Document Anonymizer</h1>
        <div class="upload-area" id="dropZone" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📄</div>
            <p class="upload-text">Drag & Drop DOCX file here or click to select</p>
            <input type="file" id="fileInput" accept=".docx" />
        </div>

        <div class="fields-container">
            <div class="fields-title">Fields to Anonymize:</div>
            <div class="fields-list">
                <span class="field-tag">NAME</span>
                <span class="field-tag">PRN</span>
                <span class="field-tag">BRANCH</span>
                <span class="field-tag">BATCH</span>
                <span class="field-tag">SUB (preserved)</span>
            </div>
            <input type="text" class="custom-field" id="customFields" 
                   placeholder="Add custom fields (comma-separated, e.g.: ROLL,EMAIL,PHONE)" />
        </div>

        <button class="btn" id="anonymizeBtn" disabled>Anonymize Document</button>
        <div class="loading" id="loading">Processing your document</div>
        <div id="result"></div>
    </div>

    <script>
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const customFields = document.getElementById('customFields');
        const anonymizeBtn = document.getElementById('anonymizeBtn');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        let currentFile = null;

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);
        fileInput.addEventListener('change', handleFiles, false);
        anonymizeBtn.addEventListener('click', anonymizeDocument);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            dropZone.classList.add('dragover');
        }

        function unhighlight(e) {
            dropZone.classList.remove('dragover');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles({ target: { files: files } });
        }

        function handleFiles(e) {
            const files = e.target.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.name.toLowerCase().endsWith('.docx')) {
                    currentFile = file;
                    anonymizeBtn.disabled = false;
                    showResult('success', `File selected: ${file.name}`);
                } else {
                    showResult('error', 'Please select a DOCX file');
                    anonymizeBtn.disabled = true;
                    currentFile = null;
                }
            }
        }

        async function anonymizeDocument() {
            if (!currentFile) return;

            const formData = new FormData();
            formData.append('file', currentFile);
            
            // Add custom fields if provided
            const customFieldsValue = customFields.value.trim();
            if (customFieldsValue) {
                formData.append('fields', customFieldsValue);
            }

            loading.style.display = 'block';
            result.style.display = 'none';
            anonymizeBtn.disabled = true;

            try {
                const response = await fetch('/anonymize/upload', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    // Get the filename from the Content-Disposition header
                    const contentDisposition = response.headers.get('Content-Disposition');
                    const filename = contentDisposition.split('filename=')[1].replace(/"/g, '');
                    
                    // Get replacements from custom header
                    const replacements = response.headers.get('replacements');
                    
                    // Convert the response to a blob
                    const blob = await response.blob();
                    
                    // Create download link
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showResult('success', `
                        Document anonymized successfully!<br>
                        The anonymized document has been downloaded automatically.<br>
                        <br>
                        Replacements made: ${replacements || 'None'}
                    `);
                } else {
                    const data = await response.json();
                    throw new Error(data.message || 'Anonymization failed');
                }
            } catch (error) {
                showResult('error', `Error: ${error.message}`);
            } finally {
                loading.style.display = 'none';
                anonymizeBtn.disabled = false;
            }
        }

        function showResult(type, message) {
            result.innerHTML = message;
            result.className = type;
            result.style.display = 'block';
        }
    </script>
</body>
</html> 