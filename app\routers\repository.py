from fastapi import APIRouter, Request, Depends, HTTPException, Form
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from app.database import get_db
from app.dependencies import require_auth, get_user_repository
from app.services.repository_service import RepositoryService
from app.models import User, Repository
import logging

logger = logging.getLogger(__name__)
router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/", response_class=HTMLResponse)
async def repository_page(request: Request, user: User = Depends(require_auth)):
    """Show repository management page."""
    return templates.TemplateResponse("dashboard/repository.html", {
        "request": request,
        "user": user,
        "repository": user.repository,
        "can_create": RepositoryService.can_create_repository(user)
    })

@router.post("/create")
async def create_repository(
    request: Request,
    name: str = Form(...),
    description: str = Form(""),
    user: User = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """Create a new repository."""
    try:
        if not RepositoryService.can_create_repository(user):
            return JSONResponse(
                content={"error": "You already have a repository. Only one repository per user is allowed."},
                status_code=400
            )
        
        repository = RepositoryService.create_repository(db, user, name, description)
        
        return JSONResponse(content={
            "success": True,
            "message": f"Repository '{name}' created successfully",
            "repository": {
                "id": repository.id,
                "name": repository.name,
                "description": repository.description
            }
        })
        
    except ValueError as e:
        return JSONResponse(content={"error": str(e)}, status_code=400)
    except Exception as e:
        logger.error(f"Error creating repository: {e}")
        return JSONResponse(content={"error": "Failed to create repository"}, status_code=500)

@router.post("/sync-github")
async def sync_with_github(
    request: Request,
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
):
    """Sync repository with GitHub."""
    try:
        if repository.is_synced:
            return JSONResponse(
                content={"error": "Repository is already synced with GitHub"},
                status_code=400
            )
        
        updated_repository = await RepositoryService.sync_with_github(db, repository, user)
        
        return JSONResponse(content={
            "success": True,
            "message": "Repository synced with GitHub successfully",
            "github_url": updated_repository.github_repo_url
        })
        
    except ValueError as e:
        return JSONResponse(content={"error": str(e)}, status_code=400)
    except Exception as e:
        logger.error(f"Error syncing with GitHub: {e}")
        return JSONResponse(content={"error": "Failed to sync with GitHub"}, status_code=500)

@router.delete("/")
async def delete_repository(
    request: Request,
    user: User = Depends(require_auth),
    repository: Repository = Depends(get_user_repository),
    db: Session = Depends(get_db)
):
    """Delete repository."""
    try:
        success = RepositoryService.delete_repository(db, repository)
        
        if success:
            return JSONResponse(content={
                "success": True,
                "message": "Repository deleted successfully"
            })
        else:
            return JSONResponse(content={"error": "Failed to delete repository"}, status_code=500)
            
    except Exception as e:
        logger.error(f"Error deleting repository: {e}")
        return JSONResponse(content={"error": "Failed to delete repository"}, status_code=500)
