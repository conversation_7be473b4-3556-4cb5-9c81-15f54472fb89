def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes is None:
        return "Unknown"
    
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

def get_file_icon(mime_type: str = None, file_extension: str = None) -> str:
    """Get appropriate icon class for file type."""
    if mime_type:
        if mime_type.startswith('image/'):
            return 'fas fa-image'
        elif mime_type.startswith('video/'):
            return 'fas fa-video'
        elif mime_type.startswith('audio/'):
            return 'fas fa-music'
        elif mime_type.startswith('text/'):
            return 'fas fa-file-alt'
        elif 'pdf' in mime_type:
            return 'fas fa-file-pdf'
        elif 'zip' in mime_type or 'archive' in mime_type:
            return 'fas fa-file-archive'
    
    if file_extension:
        ext = file_extension.lower()
        
        # Code files
        if ext in ['py', 'js', 'html', 'css', 'java', 'cpp', 'c', 'php', 'rb', 'go', 'rs']:
            return 'fas fa-code'
        
        # Documents
        elif ext in ['doc', 'docx']:
            return 'fas fa-file-word'
        elif ext in ['xls', 'xlsx']:
            return 'fas fa-file-excel'
        elif ext in ['ppt', 'pptx']:
            return 'fas fa-file-powerpoint'
        elif ext == 'pdf':
            return 'fas fa-file-pdf'
        
        # Images
        elif ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']:
            return 'fas fa-image'
        
        # Videos
        elif ext in ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']:
            return 'fas fa-video'
        
        # Audio
        elif ext in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
            return 'fas fa-music'
        
        # Archives
        elif ext in ['zip', 'rar', '7z', 'tar', 'gz']:
            return 'fas fa-file-archive'
    
    # Default file icon
    return 'fas fa-file'
