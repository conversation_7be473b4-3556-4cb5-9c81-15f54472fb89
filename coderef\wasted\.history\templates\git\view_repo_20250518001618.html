{% extends "git/base.html" %}

{% block title %}{{ repo_name }} - Repository{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Repository Header -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">
                    <i class="fas fa-book mr-2 text-gray-500"></i>
                    {{ owner }}/{{ repo_name }}
                </h1>
                <p class="text-gray-600 mt-2">
                    {% if metadata.description %}
                        {{ metadata.description }}
                    {% else %}
                        No description
                    {% endif %}
                </p>
            </div>
            <div class="flex space-x-3">
                <button onclick="showUploadModal()" class="btn btn-outline-primary flex items-center px-4 py-2 border border-blue-500 text-blue-500 rounded hover:bg-blue-50">
                    <i class="fas fa-upload mr-2"></i>Upload File
                </button>
                <button onclick="showMkdirModal()" class="btn btn-outline-primary flex items-center px-4 py-2 border border-green-500 text-green-500 rounded hover:bg-green-50">
                    <i class="fas fa-folder-plus mr-2"></i>New Directory
                </button>
            </div>
        </div>
        
        <div class="flex space-x-4 text-sm text-gray-600">
            <span>
                <i class="fas fa-code-branch mr-1"></i>
                {% if metadata.forked_from %}
                    Forked from {{ metadata.forked_from.owner }}/{{ metadata.forked_from.name }}
                {% else %}
                    Original Repository
                {% endif %}
            </span>
            <span>
                <i class="far fa-clock mr-1"></i>
                Created {{ metadata.created_at | replace('T', ' at ') }}
            </span>
        </div>
    </div>

    <!-- Repository Contents -->
    <div class="bg-white shadow-md rounded-lg">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-folder-open mr-2"></i>Repository Contents
            </h2>
        </div>
        
        {% if structure %}
        <ul class="divide-y divide-gray-200">
            {% for item in structure %}
                {% if item.dirs or item.files %}
                <li class="px-6 py-4 hover:bg-gray-50 transition">
                    <div class="flex justify-between items-center">
                        <div>
                            <span class="font-medium text-gray-700">
                                {% if item.path %}
                                    {{ item.path }}
                                {% else %}
                                    Root Directory
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex space-x-3">
                            {% if item.dirs %}
                            <span class="text-sm text-gray-500">
                                <i class="fas fa-folder mr-1"></i>
                                {{ item.dirs | length }} Directories
                            </span>
                            {% endif %}
                            {% if item.files %}
                            <span class="text-sm text-gray-500">
                                <i class="fas fa-file mr-1"></i>
                                {{ item.files | length }} Files
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </li>
                {% endif %}
            {% endfor %}
        </ul>
        {% else %}
        <div class="p-6 text-center text-gray-500">
            <i class="fas fa-folder-open text-4xl mb-4"></i>
            <p>This repository is empty. Upload some files to get started!</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Upload File Modal -->
<div id="uploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold mb-4">Upload File</h2>
            <form id="uploadForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">File</label>
                    <input type="file" id="fileUpload" name="file" class="w-full border rounded px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">Path (Optional)</label>
                    <input type="text" id="uploadPath" name="path" class="w-full border rounded px-3 py-2" placeholder="e.g., src/components">
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeUploadModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Cancel
                    </button>
                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Upload
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Directory Modal -->
<div id="mkdirModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h2 class="text-2xl font-bold mb-4">Create Directory</h2>
            <form id="mkdirForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">Directory Path</label>
                    <input type="text" id="dirPath" name="path" class="w-full border rounded px-3 py-2" required placeholder="e.g., src/components">
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" onclick="closeMkdirModal()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        Cancel
                    </button>
                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Create
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function showUploadModal() {
    document.getElementById('uploadModal').classList.remove('hidden');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('uploadForm').reset();
}

function showMkdirModal() {
    document.getElementById('mkdirModal').classList.remove('hidden');
}

function closeMkdirModal() {
    document.getElementById('mkdirModal').classList.add('hidden');
    document.getElementById('mkdirForm').reset();
}

document.getElementById('uploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const formData = new FormData();
    const fileInput = document.getElementById('fileUpload');
    const pathInput = document.getElementById('uploadPath');
    
    if (!fileInput.files.length) {
        alert('Please select a file to upload');
        return;
    }
    
    formData.append('file', fileInput.files[0]);
    formData.append('path', pathInput.value || '');
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/upload`, {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error uploading file: ' + data.message);
        }
    } catch (error) {
        alert('Network error. Please try again.');
    }
});

document.getElementById('mkdirForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    const path = document.getElementById('dirPath').value.trim();
    
    if (!path) {
        alert('Please enter a directory path');
        return;
    }
    
    try {
        const response = await fetch(`/git/{{ owner }}/{{ repo_name }}/mkdir`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `path=${encodeURIComponent(path)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error creating directory: ' + data.message);
        }
    } catch (error) {
        alert('Network error. Please try again.');
    }
});
</script>
{% endblock %} 