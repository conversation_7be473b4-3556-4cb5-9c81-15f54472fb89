from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def wasted():
    return {"message":  "Wasted time"}

@app.get("/login")
async def login(request: Request):
    """Initiate GitHub OAuth login."""
    if not settings.is_configured:
        return templates.TemplateResponse("error.html", {
            "request": request, 
            "error_message": "GitHub OAuth is not configured. Please contact the administrator."
        })