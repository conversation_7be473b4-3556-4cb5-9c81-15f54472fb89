<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}WastedTime - GitHub Virtual File System{% endblock %}</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/main.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">
                    <i class="fas fa-code-branch"></i> WastedTime
                </a>
                
                {% if user %}
                <div class="user-info">
                    {% if user.avatar_url %}
                    <img src="{{ user.avatar_url }}" alt="Avatar" class="avatar">
                    {% endif %}
                    <span>{{ user.display_name }}</span>
                    <a href="/auth/logout" class="btn btn-secondary btn-sm">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
                {% else %}
                <div>
                    <a href="/auth/github" class="btn btn-primary">
                        <i class="fab fa-github"></i> Login with GitHub
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container">
        {% block content %}{% endblock %}
    </main>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.0/axios.min.js"></script>
    {% block extra_js %}{% endblock %}
    
    <script>
        // Global JavaScript utilities
        function showAlert(message, type = 'success') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        function handleApiError(error) {
            console.error('API Error:', error);
            const message = error.response?.data?.error || error.message || 'An error occurred';
            showAlert(message, 'danger');
        }
    </script>
</body>
</html>
