import pytest
from unittest.mock import patch

def test_health_endpoint(client):
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_home_redirect_to_login(client):
    """Test home page redirects to login when not authenticated."""
    response = client.get("/")
    assert response.status_code == 200
    # Should show login page

def test_dashboard_requires_auth(client):
    """Test dashboard requires authentication."""
    response = client.get("/dashboard")
    assert response.status_code == 302  # Redirect to login

def test_api_user_requires_auth(client):
    """Test API user endpoint requires authentication."""
    response = client.get("/api/user")
    assert response.status_code == 401

def test_repository_creation_requires_auth(client):
    """Test repository creation requires authentication."""
    response = client.post("/repo/create", data={"name": "test-repo"})
    assert response.status_code == 401

def test_filesystem_requires_auth(client):
    """Test filesystem endpoints require authentication."""
    response = client.get("/fs/")
    assert response.status_code == 401

@patch('app.services.auth_service.AuthService.get_github_user_info')
@patch('app.services.auth_service.oauth.github.authorize_access_token')
def test_auth_callback_success(mock_token, mock_user_info, client, db_session):
    """Test successful OAuth callback."""
    # Mock OAuth response
    mock_token.return_value = {"access_token": "test_token"}
    mock_user_info.return_value = {
        "id": 12345,
        "login": "testuser",
        "name": "Test User",
        "email": "<EMAIL>",
        "avatar_url": "https://example.com/avatar.jpg"
    }
    
    response = client.get("/auth/callback")
    assert response.status_code == 302  # Redirect to dashboard

def test_create_repository_api(client, db_session):
    """Test repository creation API."""
    # This would need proper authentication setup
    # For now, just test the endpoint exists
    response = client.post("/repo/create", data={"name": "test-repo"})
    assert response.status_code == 401  # Unauthorized without auth

def test_upload_file_api(client, db_session):
    """Test file upload API."""
    # This would need proper authentication setup
    response = client.post("/fs/upload-file")
    assert response.status_code == 401  # Unauthorized without auth
