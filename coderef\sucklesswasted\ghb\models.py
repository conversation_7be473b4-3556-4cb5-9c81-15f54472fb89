from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy import create_engine
from datetime import datetime
from pathlib import Path

Base = declarative_base()

class Folder(Base):
    __tablename__ = 'folders'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    parent_id = Column(Integer, ForeignKey('folders.id'), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    parent = relationship("Folder", remote_side=[id], backref="children")
    files = relationship("File", back_populates="folder", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Folder(id={self.id}, name='{self.name}', parent_id={self.parent_id})>"
    
    def get_full_path(self):
        """Get the full path from root to this folder"""
        if self.parent_id is None:
            return "/"
        
        path_parts = []
        current = self
        while current.parent_id is not None:
            path_parts.append(current.name)
            current = current.parent
        
        path_parts.reverse()
        return "/" + "/".join(path_parts) + "/"
    
    @classmethod
    def get_or_create_path(cls, session, path: str):
        """Get or create a folder path, creating intermediate folders as needed"""
        if path == "/" or path == "":
            # Return root folder (create if doesn't exist)
            root = session.query(cls).filter_by(parent_id=None, name="root").first()
            if not root:
                root = cls(name="root", parent_id=None)
                session.add(root)
                session.commit()
            return root
        
        # Split path into parts
        path = path.strip("/")
        parts = path.split("/")
        
        # Start from root
        current_folder = cls.get_or_create_path(session, "/")
        
        # Create each part of the path
        for part in parts:
            if not part:  # Skip empty parts
                continue
                
            child = session.query(cls).filter_by(
                parent_id=current_folder.id, 
                name=part
            ).first()
            
            if not child:
                child = cls(name=part, parent_id=current_folder.id)
                session.add(child)
                session.commit()
            
            current_folder = child
        
        return current_folder

class File(Base):
    __tablename__ = 'files'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    path = Column(Text, nullable=True)  # Full path (optional)
    folder_id = Column(Integer, ForeignKey('folders.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    mime_type = Column(String(100), nullable=True)
    size = Column(Integer, nullable=True)
    storage_path = Column(String(500), nullable=True)  # Path to actual file in storage
    
    # Relationships
    folder = relationship("Folder", back_populates="files")
    
    def __repr__(self):
        return f"<File(id={self.id}, name='{self.name}', folder_id={self.folder_id})>"
    
    def get_full_path(self):
        """Get the full path including filename"""
        folder_path = self.folder.get_full_path()
        if folder_path == "/":
            return f"/{self.name}"
        return f"{folder_path.rstrip('/')}/{self.name}"

# Database setup
BASE_DIR = Path(__file__).resolve().parent.parent
DB_PATH = BASE_DIR / "file_metadata_new5.db"

# Create engine and session
engine = create_engine(f"sqlite:///{DB_PATH}", echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize the database with tables"""
    Base.metadata.create_all(bind=engine)
    
    # Create root folder if it doesn't exist
    db = SessionLocal()
    try:
        root = db.query(Folder).filter_by(parent_id=None, name="root").first()
        if not root:
            root = Folder(name="root", parent_id=None)
            db.add(root)
            db.commit()
    finally:
        db.close() 