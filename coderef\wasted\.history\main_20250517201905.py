from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Request
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import shutil
from pathlib import Path
from typing import Optional
import os
import google.generativeai as genai
from dotenv import load_dotenv
from PyPDF2 import PdfReader
import magic
import base64

# Load environment variables
load_dotenv()

# Configure Gemini API
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
genai.configure(api_key=GOOGLE_API_KEY)

app = FastAPI(title="File Upload API")

# Create uploads directory if it doesn't exist
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# Create templates directory if it doesn't exist
TEMPLATES_DIR = Path("templates")
TEMPLATES_DIR.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

async def analyze_with_gemini(content: str, mime_type: str, filename: str) -> str:
    """
    Analyze file contents using Gemini API
    """
    try:
        model = genai.GenerativeModel('gemini-pro')
        prompt = f"""Analyze this file content from {filename} and explain what it contains. 
        If it's code, explain what the code does. If it's text, summarize the main points.
        Be detailed but concise in your analysis."""
        
        response = model.generate_content(prompt + "\n\nContent:\n" + content)
        return response.text
    except Exception as e:
        return f"Error analyzing content with Gemini: {str(e)}"

async def extract_pdf_text(file_path: Path) -> str:
    """
    Extract text from PDF file
    """
    try:
        reader = PdfReader(file_path)
        text = ""
        for page in reader.pages:
            text += page.extract_text() + "\n"
        return text
    except Exception as e:
        return f"Error extracting PDF text: {str(e)}"

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Render the upload form"""
    return templates.TemplateResponse("upload.html", {"request": request})

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    Upload a file and return its contents
    """
    try:
        # Read the contents of the file
        contents = await file.read()
        
        # Save the file
        file_path = UPLOAD_DIR / file.filename
        with open(file_path, "wb") as f:
            f.write(contents)
        
        # Return the contents as text if possible, otherwise return binary data info
        try:
            text_contents = contents.decode('utf-8')
            return {
                "filename": file.filename,
                "content_type": file.content_type,
                "contents": text_contents
            }
        except UnicodeDecodeError:
            return {
                "filename": file.filename,
                "content_type": file.content_type,
                "size_bytes": len(contents),
                "message": "File contents are binary and cannot be displayed as text"
            }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while processing the file: {str(e)}"}
        )

@app.post("/analyze")
async def analyze_file(file: UploadFile = File(...)):
    """
    Upload a file and analyze its contents using Gemini
    """
    try:
        contents = await file.read()
        file_path = UPLOAD_DIR / file.filename
        
        # Save the file temporarily
        with open(file_path, "wb") as f:
            f.write(contents)
        
        # Detect file type
        mime_type = magic.from_file(str(file_path), mime=True)
        
        # Handle different file types
        if mime_type == "application/pdf":
            text_content = await extract_pdf_text(file_path)
        else:
            try:
                text_content = contents.decode('utf-8')
            except UnicodeDecodeError:
                return JSONResponse(
                    status_code=400,
                    content={"message": "File type not supported for analysis"}
                )
        
        # Analyze with Gemini
        analysis = await analyze_with_gemini(text_content, mime_type, file.filename)
        
        return {
            "filename": file.filename,
            "content_type": mime_type,
            "analysis": analysis
        }
            
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"message": f"An error occurred while analyzing the file: {str(e)}"}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 