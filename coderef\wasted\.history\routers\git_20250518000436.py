from fastapi import APIRouter, UploadFile, File, Request, Form, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from pathlib import Path
import shutil
import os
from typing import Optional
from datetime import datetime
import json

router = APIRouter(prefix="/git", tags=["git"])

# Templates
templates = Jinja2Templates(directory="templates")

# Create necessary directories
REPOS_DIR = Path("repositories")
REPOS_DIR.mkdir(exist_ok=True)

class Repository:
    def __init__(self, name: str, owner: str):
        self.name = name
        self.owner = owner
        self.created_at = datetime.now().isoformat()
        self.path = REPOS_DIR / owner / name
        
    def create(self):
        """Create a new repository directory structure"""
        self.path.mkdir(parents=True, exist_ok=True)
        self.save_metadata()
        
    def save_metadata(self):
        """Save repository metadata"""
        metadata = {
            "name": self.name,
            "owner": self.owner,
            "created_at": self.created_at,
            "forked_from": None
        }
        with open(self.path / "repo_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
            
    @staticmethod
    def load_metadata(repo_path: Path) -> dict:
        """Load repository metadata"""
        try:
            with open(repo_path / "repo_metadata.json", "r") as f:
                return json.load(f)
        except FileNotFoundError:
            return {}

    def fork(self, new_owner: str) -> 'Repository':
        """Fork the repository to a new owner"""
        new_repo = Repository(self.name, new_owner)
        if new_repo.path.exists():
            raise HTTPException(status_code=400, detail="Repository already exists")
            
        # Copy repository contents
        shutil.copytree(self.path, new_repo.path)
        
        # Update metadata for forked repo
        metadata = self.load_metadata(new_repo.path)
        metadata["owner"] = new_owner
        metadata["forked_from"] = {"owner": self.owner, "name": self.name}
        
        with open(new_repo.path / "repo_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
            
        return new_repo

@router.get("/")
async def git_home(request: Request):
    """Render the git home page"""
    # List all repositories
    repos = []
    for owner_dir in REPOS_DIR.iterdir():
        if owner_dir.is_dir():
            for repo_dir in owner_dir.iterdir():
                if repo_dir.is_dir():
                    metadata = Repository.load_metadata(repo_dir)
                    repos.append(metadata)
    
    return templates.TemplateResponse(
        "git/home.html",
        {"request": request, "repos": repos}
    )

@router.get("/new")
async def new_repository_page(request: Request):
    """Render the new repository page"""
    return templates.TemplateResponse(
        "git/new_repo.html",
        {"request": request}
    )

@router.post("/new")
async def create_repository(
    request: Request,
    name: str = Form(...),
    owner: str = Form(...),
):
    """Create a new repository"""
    try:
        repo = Repository(name, owner)
        repo.create()
        return JSONResponse({"status": "success", "message": "Repository created successfully"})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.get("/{owner}/{repo_name}")
async def view_repository(
    request: Request, 
    owner: str, 
    repo_name: str,
    path: str = ""
):
    """View repository contents"""
    repo_path = REPOS_DIR / owner / repo_name
    if not repo_path.exists():
        raise HTTPException(status_code=404, detail="Repository not found")
        
    # Get repository structure
    structure = []
    for root, dirs, files in os.walk(repo_path):
        rel_path = Path(root).relative_to(repo_path)
        if rel_path == Path("."):
            rel_path = Path("")
            
        # Skip metadata file and hidden files
        files = [f for f in files if f != "repo_metadata.json" and not f.startswith(".")]
        dirs[:] = [d for d in dirs if not d.startswith(".")]
            
        structure.append({
            "path": str(rel_path),
            "dirs": dirs,
            "files": files
        })
    
    metadata = Repository.load_metadata(repo_path)
    
    return templates.TemplateResponse(
        "git/view_repo.html",
        {
            "request": request,
            "owner": owner,
            "repo_name": repo_name,
            "structure": structure,
            "metadata": metadata,
            "current_path": path
        }
    )

@router.post("/{owner}/{repo_name}/upload")
async def upload_file(
    request: Request,
    owner: str,
    repo_name: str,
    file: UploadFile = File(...),
    path: str = Form(""),
):
    """Upload a file to the repository"""
    try:
        repo_path = REPOS_DIR / owner / repo_name
        if not repo_path.exists():
            raise HTTPException(status_code=404, detail="Repository not found")
            
        # Create target directory if it doesn't exist
        target_dir = repo_path / path
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # Save the file
        file_path = target_dir / file.filename
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)
            
        return JSONResponse({"status": "success", "message": "File uploaded successfully"})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.post("/{owner}/{repo_name}/mkdir")
async def create_directory(
    request: Request,
    owner: str,
    repo_name: str,
    path: str = Form(...),
):
    """Create a new directory in the repository"""
    try:
        repo_path = REPOS_DIR / owner / repo_name
        if not repo_path.exists():
            raise HTTPException(status_code=404, detail="Repository not found")
            
        # Create the directory
        new_dir = repo_path / path
        new_dir.mkdir(parents=True, exist_ok=True)
        
        return JSONResponse({"status": "success", "message": "Directory created successfully"})
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )

@router.post("/{owner}/{repo_name}/fork")
async def fork_repository(
    request: Request,
    owner: str,
    repo_name: str,
    new_owner: str = Form(...),
):
    """Fork a repository"""
    try:
        repo = Repository(repo_name, owner)
        if not repo.path.exists():
            raise HTTPException(status_code=404, detail="Repository not found")
            
        new_repo = repo.fork(new_owner)
        return JSONResponse({
            "status": "success",
            "message": "Repository forked successfully",
            "new_repo": {
                "owner": new_owner,
                "name": repo_name
            }
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        ) 