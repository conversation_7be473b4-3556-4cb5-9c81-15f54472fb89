from sqlalchemy import create_engine, Column, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import json
from pathlib import Path

# Create the database engine
DATABASE_URL = "sqlite:///./doccment_temcmatesedb"
engine = create_engine(DATABASE_URL)

# Create declarative base
Base = declarative_base()

class Template(Base):
    __tablename__ = "templates"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, unique=True, index=True)
    original_filename = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    placeholders = relationship("Placeholder", back_populates="template")

class Placeholder(Base):
    __tablename__ = "placeholders"
    
    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("templates.id"))
    field_name = Column(String)
    original_value = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    template = relationship("Template", back_populates="placeholders")

# Create all tables
Base.metadata.create_all(bind=engine)

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()