from sqlalchemy.orm import Session
from app.models import User, Repository, Folder
from app.services.github_service import GitHubService
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class RepositoryService:
    """Service for repository management operations."""
    
    @staticmethod
    def create_repository(db: Session, user: User, name: str, description: str = "") -> Repository:
        """Create a new repository for a user."""
        # Check if user already has a repository
        if user.repository:
            raise ValueError("User already has a repository. Only one repository per user is allowed.")
        
        # Validate repository name
        if not name or len(name.strip()) == 0:
            raise ValueError("Repository name cannot be empty")
        
        name = name.strip()
        
        # Create repository
        repository = Repository(
            name=name,
            description=description,
            user_id=user.id
        )
        
        db.add(repository)
        db.flush()  # Get the ID without committing
        
        # Create root folder for the repository
        root_folder = Folder(
            name="root",
            parent_id=None,
            repository_id=repository.id
        )
        
        db.add(root_folder)
        db.commit()
        db.refresh(repository)
        
        logger.info(f"Created repository '{name}' for user {user.username}")
        return repository
    
    @staticmethod
    async def sync_with_github(db: Session, repository: Repository, user: User) -> Repository:
        """Sync repository with GitHub."""
        if not user.access_token:
            raise ValueError("User does not have GitHub access token")
        
        github_service = GitHubService(user.access_token)
        
        try:
            # Create GitHub repository
            github_repo = await github_service.create_repository(
                name=repository.name,
                description=repository.description or "",
                private=False  # Default to public for now
            )
            
            # Update repository with GitHub info
            repository.github_repo_name = github_repo['name']
            repository.github_repo_url = github_repo['html_url']
            repository.is_synced = True
            
            db.commit()
            db.refresh(repository)
            
            logger.info(f"Successfully synced repository '{repository.name}' with GitHub")
            return repository
            
        except Exception as e:
            logger.error(f"Failed to sync repository with GitHub: {e}")
            raise
    
    @staticmethod
    def get_user_repository(db: Session, user: User) -> Optional[Repository]:
        """Get user's repository."""
        return user.repository
    
    @staticmethod
    def delete_repository(db: Session, repository: Repository) -> bool:
        """Delete a repository and all its contents."""
        try:
            # Delete repository (cascade will handle folders and files)
            db.delete(repository)
            db.commit()
            
            logger.info(f"Deleted repository: {repository.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete repository: {e}")
            db.rollback()
            return False
    
    @staticmethod
    def can_create_repository(user: User) -> bool:
        """Check if user can create a repository."""
        return user.repository is None
