{% extends "git/base.html" %}

{% block title %}Git Repositories{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Your Repositories</h1>
        <a href="/git/new" class="btn btn-primary bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition">
            <i class="fas fa-plus mr-2"></i>New Repository
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        {% if repos %}
        <ul class="divide-y divide-gray-200">
            {% for repo in repos %}
            <li class="px-6 py-4 hover:bg-gray-50 transition">
                <div class="flex justify-between items-center">
                    <div>
                        <a href="/git/{{ repo.owner }}/{{ repo.name }}" class="text-xl font-semibold text-blue-600 hover:underline">
                            {{ repo.owner }}/{{ repo.name }}
                        </a>
                        <p class="text-sm text-gray-500 mt-1">
                            Created {{ repo.created_at | replace('T', ' at ') }}
                        </p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-500">
                            <i class="fas fa-code-branch mr-1"></i>
                            {% if repo.forked_from %}Forked{% else %}Original{% endif %}
                        </span>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
        {% else %}
        <div class="p-6 text-center text-gray-500">
            <i class="fas fa-folder-open text-4xl mb-4"></i>
            <p>No repositories found. Create your first repository!</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentRepo = null;

function forkRepository(owner, name) {
    currentRepo = { owner, name };
    document.getElementById('forkModal').classList.remove('hidden');
}

function closeForkModal() {
    document.getElementById('forkModal').classList.add('hidden');
    document.getElementById('forkForm').reset();
}

document.getElementById('forkForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const newOwner = document.getElementById('newOwner').value;
    
    try {
        const response = await fetch(`/git/${currentRepo.owner}/${currentRepo.name}/fork`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `new_owner=${encodeURIComponent(newOwner)}`
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            window.location.reload();
        } else {
            alert('Error forking repository: ' + data.message);
        }
    } catch (error) {
        alert('Error forking repository: ' + error.message);
    }
});
</script>
{% endblock %} 