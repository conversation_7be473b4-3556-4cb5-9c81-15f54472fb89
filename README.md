# WastedTime - GitHub Virtual File System

A modern web application that combines GitHub OAuth authentication with a virtual file system, allowing users to create and manage virtual repositories that can be synced with GitHub.

## Features

### 🔐 Authentication
- **GitHub OAuth Integration**: Secure login with GitHub accounts
- **Session Management**: Persistent user sessions with secure token storage
- **Repository Permissions**: Includes `repo` scope for GitHub repository access

### 📁 Virtual File System
- **Virtual Repository**: Each user gets exactly one virtual repository
- **Folder Management**: Create, browse, and delete virtual folders
- **File Operations**: Upload, view, and delete files with metadata storage
- **Path Navigation**: Intuitive breadcrumb navigation
- **File Storage**: Hybrid approach - metadata in SQLite, files in storage directory

### 🔄 GitHub Integration
- **Repository Creation**: Sync virtual repository with actual GitHub repository
- **File Synchronization**: Push virtual files to GitHub (Phase 2 - planned)
- **Two-Phase Approach**: 
  - Phase 1: Temporary access token for basic operations
  - Phase 2: Full user repository permissions

### 🎨 User Interface
- **Responsive Design**: Clean, modern interface with CSS Grid/Flexbox
- **File Explorer**: GitHub-like file browser with icons and actions
- **Dashboard**: Comprehensive user and repository status overview
- **Modals**: Interactive forms for file uploads and folder creation

## Architecture

### Project Structure
```
wastedtime/
├── app/
│   ├── models/          # Database models (User, Repository, Folder, File)
│   ├── services/        # Business logic (Auth, GitHub, FileSystem, Repository)
│   ├── routers/         # API routes (auth, filesystem, repository, api)
│   ├── utils/           # Utilities (validators, helpers)
│   ├── main.py          # FastAPI application
│   ├── config.py        # Configuration management
│   ├── database.py      # Database setup
│   └── dependencies.py  # Common dependencies
├── templates/           # Jinja2 templates
├── static/             # CSS, JS, images
├── storage/            # File storage directory
└── requirements.txt    # Python dependencies
```

### Database Schema
- **Users**: GitHub user information with access tokens
- **Repositories**: One-to-one relationship with users
- **Folders**: Hierarchical folder structure
- **Files**: File metadata with storage references

### Key Design Decisions
1. **One Repository Per User**: Enforced at database level with unique constraint
2. **Hybrid File Storage**: Metadata in database, actual files in filesystem
3. **Service Layer**: Clean separation of business logic from API routes
4. **Template Inheritance**: Consistent UI with base template
5. **Error Handling**: Comprehensive error handling with user-friendly messages

## Setup Instructions

### 1. Prerequisites
- Python 3.8+
- GitHub account for OAuth app creation

### 2. GitHub OAuth Setup
1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the application details:
   - **Application name**: WastedTime
   - **Homepage URL**: `http://localhost:8000`
   - **Authorization callback URL**: `http://localhost:8000/auth/callback`
4. Copy your Client ID and Client Secret

### 3. Installation
```bash
# Clone the repository
git clone <repository-url>
cd wastedtime

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

### 4. Configuration

```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your settings
```

**Required Settings:**
```env
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here
SECRET_KEY=your-secure-secret-key-here
```

**Generate a secure secret key:**
```bash
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### 5. Run the Application
```bash
python run.py
```

Visit `http://localhost:8000` in your browser.

## Usage

### Getting Started
1. **Login**: Click "Login with GitHub" to authenticate
2. **Create Repository**: Go to Repository Settings and create your virtual repository
3. **Manage Files**: Use the file explorer to upload files and create folders
4. **Sync with GitHub**: Sync your virtual repository to create an actual GitHub repository

### File Operations
- **Upload Files**: Drag and drop or use the upload button
- **Create Folders**: Use the "New Folder" button in the file explorer
- **Navigate**: Click on folders to browse or use breadcrumb navigation
- **Delete**: Use the trash icon to delete files or folders

### Repository Management
- **One Repository Rule**: Each user can create exactly one repository
- **GitHub Sync**: Sync your virtual repository to GitHub for backup and sharing
- **Repository Settings**: Manage repository details and GitHub integration

## API Endpoints

### Authentication
- `GET /auth/github` - Initiate GitHub OAuth
- `GET /auth/callback` - OAuth callback handler
- `GET /auth/logout` - Logout user

### Repository Management
- `POST /repo/create` - Create new repository
- `POST /repo/sync-github` - Sync with GitHub
- `DELETE /repo/` - Delete repository

### File System
- `GET /fs/` - File explorer interface
- `POST /fs/create-folder` - Create folder
- `POST /fs/upload-file` - Upload file
- `DELETE /fs/folder/{id}` - Delete folder
- `DELETE /fs/file/{id}` - Delete file

### API
- `GET /api/user` - Get current user info
- `GET /api/repository` - Get repository info
- `GET /api/filesystem` - Get directory contents

## Technology Stack

- **Backend**: FastAPI, Python 3.8+
- **Database**: SQLAlchemy with SQLite
- **Authentication**: Authlib with GitHub OAuth
- **Templates**: Jinja2
- **Styling**: Custom CSS with Font Awesome icons
- **HTTP Client**: HTTPX for async GitHub API calls
- **File Storage**: Local filesystem with UUID-based naming

## Security Features

- **Secure Sessions**: Session-based authentication with secure cookies
- **Token Storage**: GitHub access tokens stored securely in database
- **Input Validation**: Comprehensive validation for file names and paths
- **Error Handling**: Safe error messages without sensitive information exposure
- **File Upload Security**: File type validation and size limits

## Future Enhancements

### Phase 2 - Full GitHub Integration
- **File Synchronization**: Automatic sync of virtual files to GitHub
- **Branch Management**: Support for Git branches in virtual repository
- **Conflict Resolution**: Handle conflicts between virtual and GitHub files
- **Real-time Sync**: WebSocket-based real-time synchronization

### Additional Features
- **File Versioning**: Track file changes and versions
- **Collaboration**: Share repositories with other users
- **File Preview**: In-browser preview for common file types
- **Search**: Full-text search across files and folders
- **Backup**: Automated backup of virtual repositories

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
