# WastedTime Configuration Guide

This document provides comprehensive information about configuring the WastedTime application using environment variables.

## Quick Start

### 1. Generate Configuration
```bash
# Create .env file with secure defaults
python scripts/generate_config.py env

# Generate a secure secret key
python scripts/generate_config.py secret

# Get GitHub OAuth setup instructions
python scripts/generate_config.py github

# Validate your configuration
python scripts/generate_config.py validate
```

### 2. Minimum Required Settings
```env
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
SECRET_KEY=your-secure-secret-key
```

## Configuration Categories

### 🔐 Security Settings
| Variable | Default | Description |
|----------|---------|-------------|
| `SECRET_KEY` | ⚠️ Must change | Session encryption key (32+ chars) |
| `FORCE_HTTPS` | `False` | Redirect HTTP to HTTPS (production) |
| `SESSION_COOKIE_SECURE` | `False` | Secure cookies (HTTPS only) |
| `SESSION_COOKIE_HTTPONLY` | `True` | Prevent XSS attacks |
| `SESSION_TIMEOUT` | `86400` | Session timeout (seconds) |

### 🔑 GitHub OAuth
| Variable | Default | Description |
|----------|---------|-------------|
| `GITHUB_CLIENT_ID` | ⚠️ Required | GitHub OAuth app client ID |
| `GITHUB_CLIENT_SECRET` | ⚠️ Required | GitHub OAuth app secret |
| `GITHUB_API_TIMEOUT` | `30` | API request timeout (seconds) |
| `GITHUB_API_MAX_RETRIES` | `3` | Max retry attempts |

### 🗄️ Database
| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `sqlite:///./wastedtime.db` | Database connection string |
| `TEST_DATABASE_URL` | `sqlite:///./test.db` | Test database URL |

### 📁 File Storage
| Variable | Default | Description |
|----------|---------|-------------|
| `STORAGE_DIR` | `storage` | File storage directory |
| `MAX_FILE_SIZE` | `10485760` | Max file size (10MB) |
| `MAX_REPOSITORY_SIZE` | `1073741824` | Max repo size (1GB) |
| `MAX_FILENAME_LENGTH` | `255` | Max filename length |

### 🚫 File Restrictions
| Variable | Default | Description |
|----------|---------|-------------|
| `ALLOWED_FILE_EXTENSIONS` | (empty) | Allowed extensions (comma-separated) |
| `BLOCKED_FILE_EXTENSIONS` | `.exe,.bat,...` | Blocked extensions |

### 🌐 Server
| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | Server bind address |
| `PORT` | `8000` | Server port |
| `ENVIRONMENT` | `development` | Environment mode |
| `DEBUG` | `True` | Debug mode |

### 📊 Rate Limiting
| Variable | Default | Description |
|----------|---------|-------------|
| `ENABLE_RATE_LIMITING` | `True` | Enable rate limiting |
| `RATE_LIMIT_PER_MINUTE` | `60` | Requests per minute per IP |
| `RATE_LIMIT_PER_HOUR` | `1000` | Requests per hour per user |

### 📝 Logging
| Variable | Default | Description |
|----------|---------|-------------|
| `LOG_LEVEL` | `INFO` | Log level (DEBUG/INFO/WARNING/ERROR) |
| `LOG_FILE` | (empty) | Log file path (console if empty) |
| `LOG_REQUESTS` | `True` | Log HTTP requests |

## Environment-Specific Configurations

### Development
```env
ENVIRONMENT=development
DEBUG=True
AUTO_RELOAD=True
SHOW_ERROR_DETAILS=True
DATABASE_URL=sqlite:///./wastedtime.db
SESSION_COOKIE_SECURE=False
```

### Production
```env
ENVIRONMENT=production
DEBUG=False
AUTO_RELOAD=False
SHOW_ERROR_DETAILS=False
DATABASE_URL=********************************/wastedtime
FORCE_HTTPS=True
SESSION_COOKIE_SECURE=True
TRUSTED_HOSTS=yourdomain.com,www.yourdomain.com
LOG_LEVEL=WARNING
LOG_FILE=/var/log/wastedtime/app.log
```

### Testing
```env
ENVIRONMENT=testing
DEBUG=True
TEST_DATABASE_URL=sqlite:///./test.db
TEST_STORAGE_DIR=test_storage
DISABLE_AUTH=False
```

## Database Configuration Examples

### SQLite (Development)
```env
DATABASE_URL=sqlite:///./wastedtime.db
```

### PostgreSQL (Production)
```env
DATABASE_URL=postgresql://username:password@localhost:5432/wastedtime
```

### MySQL
```env
DATABASE_URL=mysql://username:password@localhost:3306/wastedtime
```

## Security Best Practices

### ✅ Required for Production
- [ ] Change `SECRET_KEY` from default value
- [ ] Set `FORCE_HTTPS=True`
- [ ] Set `SESSION_COOKIE_SECURE=True`
- [ ] Configure `TRUSTED_HOSTS`
- [ ] Use PostgreSQL instead of SQLite
- [ ] Set appropriate file size limits
- [ ] Enable rate limiting
- [ ] Configure proper logging

### ⚠️ Security Warnings
- Never commit `.env` file to version control
- Use strong, unique secret keys (32+ characters)
- Regularly rotate secrets and tokens
- Monitor logs for suspicious activity
- Keep dependencies updated

## Validation

### Automatic Validation
The application automatically validates configuration on startup and reports issues.

### Manual Validation
```bash
# Check configuration status
python scripts/generate_config.py validate
```

### Common Issues
- `SECRET_KEY` using default value
- Missing GitHub OAuth credentials
- Debug mode enabled in production
- Insecure session settings in production
- File size limits exceeding GitHub limits

## Configuration Tools

### Generate Secure Values
```bash
# Generate secret key
python scripts/generate_config.py secret

# Generate password
python scripts/generate_config.py password 20

# Show database examples
python scripts/generate_config.py database

# Show security recommendations
python scripts/generate_config.py security
```

## Troubleshooting

### Configuration Not Loading
1. Check `.env` file exists in project root
2. Verify file permissions
3. Check for syntax errors in `.env`
4. Ensure no spaces around `=` in assignments

### OAuth Issues
1. Verify GitHub OAuth app settings
2. Check callback URL matches exactly
3. Ensure client ID and secret are correct
4. Check GitHub app is not suspended

### Database Issues
1. Verify database connection string
2. Check database server is running
3. Ensure user has proper permissions
4. Test connection manually

### File Upload Issues
1. Check storage directory permissions
2. Verify file size limits
3. Check available disk space
4. Review file type restrictions

For more help, see [SETUP.md](../SETUP.md) or create an issue on GitHub.
