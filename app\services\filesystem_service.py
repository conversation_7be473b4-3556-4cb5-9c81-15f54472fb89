import os
import uuid
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.models import Folder, File, Repository
import mimetypes
import logging

logger = logging.getLogger(__name__)

class FileSystemService:
    """Service for virtual file system operations."""
    
    def __init__(self, storage_dir: str = "storage"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
    
    def normalize_path(self, path: str) -> str:
        """Normalize a path string."""
        if not path or path == "/":
            return "/"
        
        # Remove leading/trailing slashes and normalize
        path = path.strip("/")
        if not path:
            return "/"
        
        return "/" + path
    
    def get_folder_by_path(self, db: Session, repository: Repository, path: str) -> Optional[Folder]:
        """Get a folder by its path within a repository."""
        path = self.normalize_path(path)
        
        if path == "/":
            # Return root folder
            return repository.root_folder
        
        # Split path into parts
        parts = [p for p in path.split("/") if p]
        
        # Start from root
        current_folder = repository.root_folder
        if not current_folder:
            return None
        
        # Navigate through path parts
        for part in parts:
            current_folder = db.query(Folder).filter(
                Folder.parent_id == current_folder.id,
                Folder.name == part
            ).first()
            
            if not current_folder:
                return None
        
        return current_folder
    
    def create_folder(self, db: Session, repository: Repository, path: str, name: str) -> Folder:
        """Create a new folder."""
        parent_folder = self.get_folder_by_path(db, repository, path)
        if not parent_folder:
            raise ValueError(f"Parent directory does not exist: {path}")
        
        # Check if folder already exists
        existing = db.query(Folder).filter(
            Folder.parent_id == parent_folder.id,
            Folder.name == name
        ).first()
        
        if existing:
            raise ValueError(f"Folder '{name}' already exists in {path}")
        
        new_folder = Folder(
            name=name,
            parent_id=parent_folder.id
        )
        
        db.add(new_folder)
        db.commit()
        db.refresh(new_folder)
        
        logger.info(f"Created folder: {new_folder.get_full_path()}")
        return new_folder
    
    def upload_file(self, db: Session, repository: Repository, path: str, 
                   file_name: str, file_content: bytes) -> File:
        """Upload a file to the virtual file system."""
        target_folder = self.get_folder_by_path(db, repository, path)
        if not target_folder:
            raise ValueError(f"Directory does not exist: {path}")
        
        # Check if file already exists
        existing_file = db.query(File).filter(
            File.folder_id == target_folder.id,
            File.name == file_name
        ).first()
        
        if existing_file:
            raise ValueError(f"File '{file_name}' already exists in {path}")
        
        # Generate unique storage filename
        file_id = str(uuid.uuid4())
        file_ext = os.path.splitext(file_name)[1]
        storage_filename = f"{file_id}{file_ext}"
        storage_path = self.storage_dir / storage_filename
        
        # Save file to storage
        with storage_path.open("wb") as buffer:
            buffer.write(file_content)
        
        # Get file info
        file_size = len(file_content)
        mime_type, _ = mimetypes.guess_type(file_name)
        
        # Create file record
        new_file = File(
            name=file_name,
            folder_id=target_folder.id,
            size=file_size,
            mime_type=mime_type,
            storage_path=storage_filename,
            path=f"{target_folder.get_full_path().rstrip('/')}/{file_name}"
        )
        
        db.add(new_file)
        db.commit()
        db.refresh(new_file)
        
        logger.info(f"Uploaded file: {new_file.get_full_path()}")
        return new_file
    
    def get_directory_contents(self, db: Session, repository: Repository, path: str) -> List[Dict[str, Any]]:
        """Get contents of a virtual directory."""
        folder = self.get_folder_by_path(db, repository, path)
        if not folder:
            return []
        
        items = []
        
        # Add subfolders
        subfolders = db.query(Folder).filter_by(parent_id=folder.id).all()
        for subfolder in subfolders:
            items.append({
                "id": subfolder.id,
                "name": subfolder.name,
                "type": "folder",
                "size": None,
                "last_modified": subfolder.updated_at,
                "path": subfolder.get_full_path().rstrip('/')
            })
        
        # Add files
        files = db.query(File).filter_by(folder_id=folder.id).all()
        for file in files:
            items.append({
                "id": file.id,
                "name": file.name,
                "type": "file",
                "size": file.size,
                "last_modified": file.updated_at,
                "path": file.get_full_path(),
                "mime_type": file.mime_type
            })
        
        # Sort: folders first, then files, both alphabetically
        return sorted(items, key=lambda x: (x["type"] != "folder", x["name"].lower()))
    
    def delete_folder(self, db: Session, folder_id: int) -> bool:
        """Delete a folder and all its contents."""
        folder = db.query(Folder).filter(Folder.id == folder_id).first()
        if not folder:
            return False
        
        if folder.is_root():
            raise ValueError("Cannot delete root folder")
        
        # Delete associated files from storage
        self._delete_folder_files_from_storage(db, folder)
        
        # Delete from database (cascade will handle children)
        db.delete(folder)
        db.commit()
        
        logger.info(f"Deleted folder: {folder.name}")
        return True
    
    def delete_file(self, db: Session, file_id: int) -> bool:
        """Delete a file."""
        file = db.query(File).filter(File.id == file_id).first()
        if not file:
            return False
        
        # Delete from storage
        if file.storage_path:
            storage_path = self.storage_dir / file.storage_path
            if storage_path.exists():
                storage_path.unlink()
        
        # Delete from database
        db.delete(file)
        db.commit()
        
        logger.info(f"Deleted file: {file.name}")
        return True
    
    def _delete_folder_files_from_storage(self, db: Session, folder: Folder):
        """Recursively delete all files in a folder from storage."""
        # Delete files in this folder
        files = db.query(File).filter(File.folder_id == folder.id).all()
        for file in files:
            if file.storage_path:
                storage_path = self.storage_dir / file.storage_path
                if storage_path.exists():
                    storage_path.unlink()
        
        # Recursively delete files in subfolders
        subfolders = db.query(Folder).filter(Folder.parent_id == folder.id).all()
        for subfolder in subfolders:
            self._delete_folder_files_from_storage(db, subfolder)
