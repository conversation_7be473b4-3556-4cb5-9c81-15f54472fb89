<!DOCTYPE html>
<html>
<head>
    <title>Dashboard - Wasted Time</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
        .logout-btn {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
        }
        .logout-btn:hover {
            background: #c82333;
        }
        .nav-links {
            margin-top: 2rem;
        }
        .nav-links a {
            display: inline-block;
            margin-right: 1rem;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .nav-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="user-info">
                {% if user.avatar_url %}
                <img src="{{ user.avatar_url }}" alt="Avatar" class="avatar">
                {% endif %}
                <div>
                    <h2>Welcome, {{ user.name or user.username }}!</h2>
                    <p>Username: {{ user.username }}</p>
                    {% if user.email %}
                    <p>Email: {{ user.email }}</p>
                    {% endif %}
                </div>
            </div>
            <a href="/auth/logout" class="logout-btn">Logout</a>
        </div>
        
        <h3>Dashboard</h3>
        <p>This is your dashboard. You're successfully logged in!</p>
        
        <div class="nav-links">
            <a href="/home">Home</a>
            <a href="/">Main Page</a>
        </div>
    </div>
</body>
</html> 